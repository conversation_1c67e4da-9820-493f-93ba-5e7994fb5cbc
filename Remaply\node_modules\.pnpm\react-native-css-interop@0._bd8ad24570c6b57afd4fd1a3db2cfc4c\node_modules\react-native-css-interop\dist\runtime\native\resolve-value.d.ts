import type { EasingFunction, Time } from "lightningcss";
import type { AnimatableValue } from "react-native-reanimated";
import type { InteropComponentConfig, RuntimeValueDescriptor, RuntimeValueFrame } from "../../types";
import { Effect } from "../observable";
import { ReducerState, ReducerTracking, Refs, ShorthandResult } from "./types";
export declare function resolveValue(state: ReducerState, refs: Refs, tracking: ReducerTracking, descriptor: RuntimeValueDescriptor, style: Record<string, any> | undefined, castToArray?: boolean): RuntimeValueDescriptor | ShorthandResult;
export declare function resolveAnimation(state: ReducerState, refs: Refs, [initialFrame, ...frames]: RuntimeValueFrame[], property: string, delay: number, totalDuration: number, easingFuncs: EasingFunction | EasingFunction[]): [AnimatableValue, AnimatableValue, ...AnimatableValue[]];
export declare const timeToMS: (time: Time) => number;
export declare function getEasing(timingFunction: EasingFunction, Easing: (typeof import("react-native-reanimated"))["Easing"]): ((t: number) => number) | {
    factory: () => (x: number) => number;
};
export declare function setDeep(target: Record<string, any>, paths: string[], value: any): void;
export declare function getWidth(state: ReducerState, refs: Refs, tracking: ReducerTracking): number;
export declare function getHeight(state: ReducerState, refs: Refs, tracking: ReducerTracking): number;
export declare const defaultValues: {
    backgroundColor: string;
    borderBottomColor: string;
    borderBottomLeftRadius: number;
    borderBottomRightRadius: number;
    borderBottomWidth: number;
    borderColor: string;
    borderLeftColor: string;
    borderLeftWidth: number;
    borderRadius: number;
    borderRightColor: string;
    borderRightWidth: number;
    borderTopColor: string;
    borderTopWidth: number;
    borderWidth: number;
    bottom: number;
    color: (effect: Effect) => "white" | "black";
    flex: number;
    flexBasis: number;
    flexGrow: number;
    flexShrink: number;
    fontSize: number;
    fontWeight: string;
    gap: number;
    left: number;
    lineHeight: number;
    margin: number;
    marginBottom: number;
    marginLeft: number;
    marginRight: number;
    marginTop: number;
    maxHeight: number;
    maxWidth: number;
    minHeight: number;
    minWidth: number;
    opacity: number;
    padding: number;
    paddingBottom: number;
    paddingLeft: number;
    paddingRight: number;
    paddingTop: number;
    perspective: number;
    right: number;
    rotate: string;
    rotateX: string;
    rotateY: string;
    rotateZ: string;
    scale: number;
    scaleX: number;
    scaleY: number;
    skewX: string;
    skewY: string;
    textShadowRadius: number;
    top: number;
    translateX: number;
    translateY: number;
    zIndex: number;
};
export declare function calc(state: ReducerState, refs: Refs, tracking: ReducerTracking, descriptor: RuntimeValueDescriptor | RuntimeValueDescriptor[], style?: Record<string, any>): {
    mode: string;
    raw: number;
    value: string | number;
} | undefined;
export declare function getBaseValue(state: ReducerState, paths: string[]): {
    value: any | undefined;
    defaultValue: string | number;
};
export declare function getTarget(target: Record<string, any> | undefined | null, config: InteropComponentConfig): Record<string, any> | undefined;
