{"version": 3, "file": "shadows.js", "sourceRoot": "", "sources": ["../../src/tailwind/shadows.ts"], "names": [], "mappings": ";;;;;;AAAA,mGAA2E;AAC3E,kFAA+E;AAC/E,qFAA6D;AAC7D,mGAA2E;AAC3E,gEAAwC;AAE3B,QAAA,OAAO,GAAG,IAAA,gBAAM,EAAC,CAAC,EAAE,cAAc,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,EAAE,EAAE;IACnE,MAAM,EAAE,WAAW,EAAE,GAAG,IAAW,CAAC;IACpC,MAAM,gBAAgB,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;IAElE,SAAS,YAAY,CAAC,KAAU;QAC9B,IAAI,YAAY,GAAQ,gBAAgB,CAAC,IAAI,CAC3C,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAC9B,EAAE,CAAC,CAAC,CAAC,CAAC;QAEP,IAAI,SAAS,GAAG,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;QAEnD,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;YACvD,SAAS,GAAG,CAAC,CAAC;QAChB,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,WAAW,CAAC,YAAY,EAAE;QACxB,yBAAyB,EAAE,WAAW;QACtC,kBAAkB,EAAE,WAAW;QAC/B,aAAa,EAAE,WAAW;QAC1B,qBAAqB,EAAE,WAAW;KACnC,CAAC,CAAC;IAEH,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;QAC5C,cAAc,CACZ;YACE,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;gBACnB,OAAO;oBACL,eAAe,EAAE,KAAK;iBAChB,CAAC;YACX,CAAC;SACF,EACD,EAAE,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,EAAE,CAC/B,CAAC;IACJ,CAAC;IAED,cAAc,CACZ;QACE,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;YAChB,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YAEtC,IAAI,cAAc,GAAG,IAAA,6BAAmB,EAAC,WAAW,CAAC,CAAC;YACtD,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;YAC9B,IAAI,GAAG,GAAG,IAAA,yCAAmB,EAAC,KAAK,CAAC,CAAC;YAErC,MAAM,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAE3D,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO;YACT,CAAC;YAED,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,UAAU,CAAC;YAEzC,MAAM,MAAM,GAAG;gBACb,mBAAmB,EAAE,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;gBACvD,kBAAkB,EAAE,wBAAwB;gBAC5C,2DAA2D,EAAE,MAAM;gBACnE,yBAAyB,EAAE,CAAC;gBAC5B,6DAA6D,EAAE,MAAM;gBACrE,0BAA0B,EAAE,CAAC;gBAC7B,mBAAmB,EAAE,IAAI,IAAI,CAAC;gBAC9B,oBAAoB,EAAE,CAAC;aACjB,CAAC;YAET,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;gBAC5C,MAAM,CAAC,eAAe,CAAC,GAAG,SAAS,CAAC;YACtC,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;KACF,EACD,EAAE,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,EAAE,CACjD,CAAC;IAEF,cAAc,CACZ;QACE,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;YAChB,OAAO;gBACL,mBAAmB,EAAE,IAAA,sBAAY,EAAC,KAAK,CAAC;aACzC,CAAC;QACJ,CAAC;KACF,EACD;QACE,MAAM,EAAE,IAAA,6BAAmB,EAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACpD,IAAI,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC;KACvB,CACF,CAAC;AACJ,CAAC,CAAC,CAAC"}