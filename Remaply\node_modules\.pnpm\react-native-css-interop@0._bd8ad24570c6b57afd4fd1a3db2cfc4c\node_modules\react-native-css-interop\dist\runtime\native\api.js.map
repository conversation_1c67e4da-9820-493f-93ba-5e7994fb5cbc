{"version": 3, "file": "api.js", "sourceRoot": "", "sources": ["../../../src/runtime/native/api.ts"], "names": [], "mappings": ";;;AAoGA,wCAaC;AAED,oBAiBC;AApID,iCAAwE;AAExE,yCAMsB;AAMtB,sCAA+C;AAC/C,8CAAsD;AACtD,qEAAuD;AACvD,qDAA2C;AAC3C,qCAAsE;AACtE,2DAAuD;AAEvD,2CAA0C;AAAjC,wGAAA,UAAU,OAAA;AACnB,mEAAuD;AAA9C,qHAAA,WAAW,OAAA;AACpB,uDAAyC;AAAhC,uGAAA,GAAG,OAAA;AAEC,QAAA,iBAAiB,GAAG,IAAI,GAAG,EAGrC,CAAC;AAQG,MAAM,UAAU,GAAe,CAAC,aAAa,EAAE,OAAO,EAAO,EAAE;IACpE,MAAM,OAAO,GAAG,IAAA,2BAAkB,EAAC,OAAO,CAAC,CAAC;IAE5C,IAAI,SAAc,CAAC;IACnB,MAAM,IAAI,GAAG,IAAA,oCAAgB,EAAC,aAAa,CAAC,CAAC;IAM7C,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;QACxB,SAAS,GAAG,CAAC,KAA0B,EAAE,EAAE;YACzC,OAAO,IAAA,wBAAO,EAAC,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;QAC3D,CAAC,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,SAAS,GAAG,IAAA,kBAAU,EAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YAEpC,OAAO,IAAA,wBAAO,EAAC,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;QAErD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,IAAI,GAAG,aAAa,CAAC,WAAW,IAAI,aAAa,CAAC,IAAI,IAAI,SAAS,CAAC;IAC1E,SAAS,CAAC,WAAW,GAAG,cAAc,IAAI,EAAE,CAAC;IAC7C,yBAAiB,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;IAChD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AA1BW,QAAA,UAAU,cA0BrB;AAEK,MAAM,UAAU,GAAe,CAAC,SAAc,EAAE,OAAO,EAAO,EAAE;IACrE,MAAM,OAAO,GAAG,IAAA,2BAAkB,EAAC,OAAO,CAAC,CAAC;IAE5C,MAAM,gBAAgB,GAAG,IAAA,kBAAU,EAAC,SAAS,mBAAmB,CAC9D,EAAE,GAAG,KAAK,EAAuB,EACjC,GAAQ;QAER,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,KAAK,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAGtC,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM;gBAAE,SAAS;YAEpD,MAAM,WAAW,GAAG;gBAClB,CAAC,2BAAkB,CAAC,EAAE,IAAI;aAC3B,CAAC;YACF,qBAAY,CAAC,GAAG,CAAC,WAAW,EAAE;gBAC5B,CAAC,2BAAkB,CAAC,EAAE,mBAAmB;gBACzC,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;aAChC,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAE5B,IAAA,uBAAc,EAAC,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE;gBACzC,gBAAgB,EAAE,SAAS;aAC5B,CAAC,CAAC;QACL,CAAC;QAED,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;QAChB,OAAO,IAAA,qBAAa,EAAC,SAAgB,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;IAChE,CAAC,CAAC,CAAC;IAEH,yBAAiB,CAAC,GAAG,CAAC,SAAgB,EAAE,gBAAgB,CAAC,CAAC;IAC1D,OAAO,gBAAgB,CAAC;AAC1B,CAAC,CAAC;AAlCW,QAAA,UAAU,cAkCrB;AAEF,SAAgB,cAAc;IAC5B,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,IAAA,gBAAQ,EAAS,GAAG,EAAE,CAAC,CAAC;QAClD,GAAG,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;QACvC,YAAY,EAAE,IAAI,GAAG,EAAE;KACxB,CAAC,CAAC,CAAC;IAEJ,IAAA,0BAAa,EAAC,MAAM,CAAC,CAAC;IAEtB,OAAO;QACL,WAAW,EAAE,oCAAW,CAAC,GAAG,CAAC,MAAM,CAAC;QACpC,cAAc,EAAE,oCAAW,CAAC,GAAG;QAC/B,iBAAiB,EAAE,oCAAW,CAAC,MAAM;KACtC,CAAC;AACJ,CAAC;AAED,SAAgB,IAAI,CAAC,SAAiD;IACpE,MAAM,KAAK,GAAwB,EAAE,CAAC;IAEtC,qBAAY,CAAC,GAAG,CAAC,KAAK,EAAE;QACtB,CAAC,2BAAkB,CAAC,EAAE,IAAI;QAC1B,SAAS,EAAE,IAAI;QACf,CAAC,EAAE;YACD;gBACE,CAAC,wBAAe,CAAC,EAAE,IAAI;gBACvB,CAAC,EAAE,0BAAiB;gBACpB,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE;oBACzD,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;gBAC7D,CAAC,CAAC;aACH;SACF;KACF,CAAC,CAAC;IACH,OAAO,KAAK,CAAC;AACf,CAAC;AAEM,MAAM,yBAAyB,GAAG,CAAC,IAAY,EAAE,EAAE;IACxD,MAAM,OAAO,GAAG,IAAA,kBAAU,EAAC,wBAAe,CAAC,CAAC;IAE5C,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,IAAA,gBAAQ,EAAS,GAAG,EAAE,CAAC,CAAC;QACjD,GAAG,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;QACtC,YAAY,EAAE,IAAI,GAAG,EAAE;KACxB,CAAC,CAAC,CAAC;IAEJ,IAAI,KAAK,GAAQ,IAAA,oBAAW,EAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACpD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC;QAChD,IAAA,0BAAa,EAAC,MAAM,CAAC,CAAC;QACtB,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAfW,QAAA,yBAAyB,6BAepC;AAKK,MAAM,cAAc,GAAG,GAAG,EAAE;IACjC,OAAO,CAAC,IAAI,CACV,0EAA0E,CAC3E,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,cAAc,kBAIzB"}