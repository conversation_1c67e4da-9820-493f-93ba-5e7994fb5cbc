import { Theme } from './types';

export const lightTheme: Theme = {
    colors: {
        primary: {
            main: '#2C2C2C', // Dark Gray
            light: '#4A4A4A',
            dark: '#1A1A1A',
            contrast: '#F5F5F0', // Cream White
        },
        secondary: {
            main: '#6E6E6E', // Medium Gray
            light: '#8A8A8A',
            dark: '#4A4A4A',
            contrast: '#F5F5F0',
        },
        error: {
            main: '#FF3B30', // Warm Red
            light: '#FF6B5A',
            dark: '#E52B20',
            contrast: '#ffffff',
        },
        warning: {
            main: '#ed6c02',
            light: '#ff9800',
            dark: '#e65100',
            contrast: '#ffffff',
        },
        info: {
            main: '#0288d1',
            light: '#03a9f4',
            dark: '#01579b',
            contrast: '#ffffff',
        },
        success: {
            main: '#2e7d32',
            light: '#4caf50',
            dark: '#1b5e20',
            contrast: '#ffffff',
        },
        grey: {
            50: '#fafafa',
            100: '#f5f5f5',
            200: '#eeeeee',
            300: '#e0e0e0',
            400: '#bdbdbd',
            500: '#9e9e9e',
            600: '#757575',
            700: '#616161',
            800: '#424242',
            900: '#212121',
        },
        text: {
            primary: '#1A1A1A', // Very Dark Gray
            secondary: '#4A4A4A', // Dark Gray
            disabled: 'rgba(26, 26, 26, 0.38)',
        },
        background: {
            default: '#FFFFFF', // Pure White
            paper: '#F5F5F0', // Cream White
        },
        action: {
            active: 'rgba(0, 0, 0, 0.54)',
            hover: 'rgba(0, 0, 0, 0.04)',
            selected: 'rgba(0, 0, 0, 0.08)',
            disabled: 'rgba(0, 0, 0, 0.26)',
            disabledBackground: 'rgba(0, 0, 0, 0.12)',
        },
        border: {
            primary: '#e0e0e0',
            secondary: '#f5f5f5',
            main: '#e0e0e0',
        },
    },
    typography: {
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
        fontSize: 16,
        fontWeightLight: 300,
        fontWeightRegular: 400,
        fontWeightMedium: 500,
        fontWeightBold: 700,
        textStyles: {
            h1: { fontSize: 48, fontWeight: 800, letterSpacing: -1.5 }, // Large display
            h2: { fontSize: 36, fontWeight: 700, letterSpacing: -1 }, // Medium display
            h3: { fontSize: 24, fontWeight: 600, letterSpacing: -0.5 }, // Small display
            h4: { fontSize: 20, fontWeight: 600, letterSpacing: 0 }, // Heading
            h5: { fontSize: 18, fontWeight: 500, letterSpacing: 0 }, // Subheading
            h6: { fontSize: 16, fontWeight: 500, letterSpacing: 0.15 }, // Small heading
            body1: { fontSize: 16, fontWeight: 400, letterSpacing: 0.15 }, // Regular body
            body2: { fontSize: 14, fontWeight: 400, letterSpacing: 0.25 }, // Small body
            subtitle1: { fontSize: 16, fontWeight: 500, letterSpacing: 0.15 }, // Subtitle
            subtitle2: { fontSize: 14, fontWeight: 500, letterSpacing: 0.1 }, // Small subtitle
            caption: { fontSize: 12, fontWeight: 400, letterSpacing: 0.4 }, // Caption
            overline: { fontSize: 10, fontWeight: 400, letterSpacing: 1.5 }, // Overline
            button: { fontSize: 14, fontWeight: 600, letterSpacing: 1.25 }, // Button text
        },
    },
    spacing: {
        unit: 8,
        xs: 4,
        sm: 8,
        md: 16,
        lg: 24,
        xl: 32,
    },
    shape: {
        borderRadius: 8,
    },
    shadows: [
        'none',
        '0px 2px 1px -1px rgba(0,0,0,0.2),0px 1px 1px 0px rgba(0,0,0,0.14),0px 1px 3px 0px rgba(0,0,0,0.12)',
        '0px 3px 1px -2px rgba(0,0,0,0.2),0px 2px 2px 0px rgba(0,0,0,0.14),0px 1px 5px 0px rgba(0,0,0,0.12)',
        '0px 3px 3px -2px rgba(0,0,0,0.2),0px 3px 4px 0px rgba(0,0,0,0.14),0px 1px 8px 0px rgba(0,0,0,0.12)',
        '0px 3px 5px -1px rgba(0,0,0,0.2),0px 5px 8px 0px rgba(0,0,0,0.14),0px 1px 14px 0px rgba(0,0,0,0.12)',
        '0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12)',
        '0px 4px 5px -2px rgba(0,0,0,0.2),0px 7px 10px 1px rgba(0,0,0,0.14),0px 2px 16px 1px rgba(0,0,0,0.12)',
        '0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12)',
        '0px 5px 6px -3px rgba(0,0,0,0.2),0px 9px 12px 1px rgba(0,0,0,0.14),0px 3px 16px 2px rgba(0,0,0,0.12)',
        '0px 6px 6px -3px rgba(0,0,0,0.2),0px 10px 14px 1px rgba(0,0,0,0.14),0px 4px 18px 3px rgba(0,0,0,0.12)',
        '0px 6px 7px -4px rgba(0,0,0,0.2),0px 11px 15px 1px rgba(0,0,0,0.14),0px 4px 20px 3px rgba(0,0,0,0.12)',
        '0px 7px 8px -4px rgba(0,0,0,0.2),0px 12px 17px 2px rgba(0,0,0,0.14),0px 5px 22px 4px rgba(0,0,0,0.12)',
        '0px 7px 8px -4px rgba(0,0,0,0.2),0px 13px 19px 2px rgba(0,0,0,0.14),0px 5px 24px 4px rgba(0,0,0,0.12)',
        '0px 7px 9px -4px rgba(0,0,0,0.2),0px 14px 21px 2px rgba(0,0,0,0.14),0px 5px 26px 4px rgba(0,0,0,0.12)',
        '0px 8px 9px -5px rgba(0,0,0,0.2),0px 15px 22px 2px rgba(0,0,0,0.14),0px 6px 28px 5px rgba(0,0,0,0.12)',
        '0px 8px 10px -5px rgba(0,0,0,0.2),0px 16px 24px 2px rgba(0,0,0,0.14),0px 6px 30px 5px rgba(0,0,0,0.12)',
        '0px 8px 11px -5px rgba(0,0,0,0.2),0px 17px 26px 2px rgba(0,0,0,0.14),0px 6px 32px 5px rgba(0,0,0,0.12)',
        '0px 9px 11px -5px rgba(0,0,0,0.2),0px 18px 28px 2px rgba(0,0,0,0.14),0px 7px 34px 6px rgba(0,0,0,0.12)',
        '0px 9px 12px -6px rgba(0,0,0,0.2),0px 19px 29px 2px rgba(0,0,0,0.14),0px 7px 36px 6px rgba(0,0,0,0.12)',
        '0px 10px 13px -6px rgba(0,0,0,0.2),0px 20px 31px 3px rgba(0,0,0,0.14),0px 8px 38px 7px rgba(0,0,0,0.12)',
        '0px 10px 13px -6px rgba(0,0,0,0.2),0px 21px 33px 3px rgba(0,0,0,0.14),0px 8px 40px 7px rgba(0,0,0,0.12)',
        '0px 10px 14px -6px rgba(0,0,0,0.2),0px 22px 35px 3px rgba(0,0,0,0.14),0px 8px 42px 7px rgba(0,0,0,0.12)',
        '0px 11px 14px -7px rgba(0,0,0,0.2),0px 23px 36px 3px rgba(0,0,0,0.14),0px 9px 44px 8px rgba(0,0,0,0.12)',
        '0px 11px 15px -7px rgba(0,0,0,0.2),0px 24px 38px 3px rgba(0,0,0,0.14),0px 9px 46px 8px rgba(0,0,0,0.12)'
    ]
};

export const darkTheme: Theme = {
    ...lightTheme,
    colors: {
        ...lightTheme.colors,
        text: {
            primary: '#F9FAFB',
            secondary: '#D1D5DB',
            disabled: '#6B7280'
        },
        background: {
            default: '#111827',
            paper: '#1F2937'
        },
        action: {
            active: 'rgba(255, 255, 255, 0.54)',
            hover: 'rgba(255, 255, 255, 0.04)',
            selected: 'rgba(255, 255, 255, 0.08)',
            disabled: 'rgba(255, 255, 255, 0.26)',
            disabledBackground: 'rgba(255, 255, 255, 0.12)'
        }
    }
}; 