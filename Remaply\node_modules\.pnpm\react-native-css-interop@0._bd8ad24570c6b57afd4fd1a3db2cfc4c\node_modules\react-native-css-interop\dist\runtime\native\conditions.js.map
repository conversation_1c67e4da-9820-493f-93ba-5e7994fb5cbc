{"version": 3, "file": "conditions.js", "sourceRoot": "", "sources": ["../../../src/runtime/native/conditions.ts"], "names": [], "mappings": ";;AAmCA,4BA0BC;AAED,4CAMC;AAKD,wCAYC;AAED,8CAqBC;AAED,gDAuBC;AAuED,sCAyBC;AAtOD,+CAAiE;AAWjE,yCAAsD;AAQtD,qEAA8E;AAE9E,yDAAiD;AAcjD,SAAgB,QAAQ,CACtB,IAAe,EACf,IAAU,EACV,QAAyB;IAGzB,IACE,IAAI,CAAC,aAAa;QAClB,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,EAClE,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QAC5E,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IACE,IAAI,CAAC,cAAc;QACnB,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,EACxD,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QAC9D,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,gBAAgB,CAC9B,KAAkB,EAClB,QAAyB,EACzB,YAA0B;IAE1B,OAAO,YAAY,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;AACxE,CAAC;AAKD,SAAgB,cAAc,CAC5B,QAAyB,EACzB,UAAsB,EACtB,qBAAyC;IACvC,KAAK,EAAE,qBAAE;IACT,MAAM,EAAE,qBAAE;CACX;IAED,MAAM,IAAI,GACR,UAAU,CAAC,SAAS,KAAK,OAAO;QAChC,aAAa,CAAC,UAAU,CAAC,SAAS,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IACpE,OAAO,UAAU,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AACvD,CAAC;AAED,SAAgB,iBAAiB,CAC/B,KAAkB,EAClB,IAAwB,EACxB,QAA0B;IAO1B,IAAI,OAAO,GAAG,IAAI,CAAC;IACnB,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,OAAO,EAAE,CAAC;QACzC,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC9C,CAAC;IACD,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,OAAO,EAAE,CAAC;QAC3C,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC/C,CAAC;IACD,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,OAAO,EAAE,CAAC;QACzC,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC9C,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAgB,kBAAkB,CAChC,IAAU,EACV,QAAyB,EACzB,cAAqD;IAGrD,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACpC,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC5C,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QAGzD,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;YAChC,MAAM,aAAa,GAAG,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YACpD,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;YACvD,OAAO,SAAS,KAAK,aAAa,IAAI,MAAM,KAAK,UAAU,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,YAAY,CACnB,KAA8B,EAC9B,IAAU;IAEV,OAAO,KAAK,CAAC,IAAI;QACf,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;QAC7B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,+BAAsB,CAAC,CAAC;AAC9C,CAAC;AAED,SAAS,aAAa,CACpB,KAA8B,EAC9B,SAAuB,EACvB,QAA0B;IAE1B,IAAI,CAAC,SAAS;QAAE,OAAO,KAAK,CAAC;IAE7B,IACE,KAAK,CAAC,aAAa;QACnB,CAAC,iBAAiB,CAAC,SAAS,EAAE,KAAK,CAAC,aAAa,EAAE,QAAQ,CAAC,EAC5D,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IACE,KAAK,CAAC,KAAK;QACX,CAAC,uBAAuB,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,CAAC,KAAK,CAAC,EAC9D,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAGD,IAAI,CAAC,KAAK,CAAC,SAAS;QAAE,OAAO,IAAI,CAAC;IAGlC,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IACvD,IAAI,CAAC,MAAM;QAAE,OAAO,KAAK,CAAC;IAE1B,OAAO,aAAa,CAClB,KAAK,CAAC,SAAS,EACf;QACE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;QAChB,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;KAClB,EACD,QAAQ,CACT,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB,CAC9B,KAA6C,EAC7C,UAAgC;IAEhC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;QACnC,MAAM,SAAS,GACb,SAAS,CAAC,IAAI,KAAK,gBAAgB;YACjC,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;YACtC,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAE9B,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC;YACzC,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAMD,SAAgB,aAAa,CAC3B,SAA6D,EAC7D,kBAAsC,EACtC,QAA0B;IAE1B,IAAI,CAAC,SAAS;QAAE,OAAO,IAAI,CAAC;IAE5B,IAAI,SAAS,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QACnC,IAAI,SAAS,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;YACjC,OAAO,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;gBACtC,OAAO,aAAa,CAAC,CAAC,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,OAAO,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;gBACrC,OAAO,aAAa,CAAC,CAAC,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;SAAM,IAAI,SAAS,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;QACpC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,KAAK,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IACvE,CAAC;SAAM,IAAI,SAAS,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAEtC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,WAAW,CAAC,SAAS,CAAC,KAAK,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;AACpE,CAAC;AAED,SAAS,WAAW,CAClB,OAAuC,EACvC,kBAAsC,EACtC,QAA0B;IAE1B,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;QACrB,KAAK,OAAO;YACV,OAAO,gBAAgB,CAAC,OAAO,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QACjE,KAAK,OAAO;YACV,OAAO,SAAS,CAAC,OAAO,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QAC1D,KAAK,SAAS;YACZ,OAAO,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACxC,KAAK,UAAU;YACb,OAAO,KAAK,CAAC;QACf;YACE,OAAuB,CAAC;IAC5B,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,gBAAgB,CACvB,OAAmE,EACnE,GAAuB,EACvB,QAA0B;IAE1B,MAAM,KAAK,GAAG,oBAAoB,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAE5D,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;QACnB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;QACrB,KAAK,YAAY;YACf,OAAO,KAAK,KAAK,yBAAU,CAAC,GAAG,EAAE,CAAC;QACpC,KAAK,cAAc;YACjB,OAAO,KAAK,KAAK,QAAQ,IAAI,uBAAQ,CAAC,EAAE,KAAK,KAAK,CAAC;QACrD,KAAK,sBAAsB;YACzB,OAAO,oCAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,KAAK,CAAC;QACrD,KAAK,OAAO;YACV,OAAO,cAAc,CAAC,OAAO,EAAE,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC7D,KAAK,WAAW;YACd,OAAO,cAAc,CAAC,oBAAoB,EAAE,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC1E,KAAK,WAAW;YACd,OAAO,cAAc,CAAC,iBAAiB,EAAE,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACvE,KAAK,QAAQ;YACX,OAAO,cAAc,CAAC,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC9D,KAAK,YAAY;YACf,OAAO,cAAc,CAAC,oBAAoB,EAAE,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC3E,KAAK,YAAY;YACf,OAAO,cAAc,CAAC,iBAAiB,EAAE,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACxE,KAAK,aAAa;YAChB,QAAQ,KAAK,EAAE,CAAC;gBACd,KAAK,WAAW;oBACd,OAAO,cAAc,CAAC,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;gBACtE,KAAK,UAAU;oBACb,OAAO,cAAc,CACnB,oBAAoB,EACpB,GAAG,CAAC,MAAM,EACV,GAAG,CAAC,KAAK,EACT,QAAQ,CACT,CAAC;YACN,CAAC;QACH;YACE,OAAO,KAAK,CAAC;IACjB,CAAC;AACH,CAAC;AAED,SAAS,oBAAoB,CAC3B,KAAwB,EACxB,QAA0B;IAE1B,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;QACnB,KAAK,QAAQ;YACX,OAAO,KAAK,CAAC,KAAK,CAAC;QACrB,KAAK,QAAQ;YACX,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;gBACjC,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;oBACpB,KAAK,IAAI;wBACP,OAAO,MAAM,CAAC,KAAK,CAAC;oBACtB,KAAK,KAAK;wBACR,OAAO,MAAM,CAAC,KAAK,GAAG,sBAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;oBAClD;wBACE,OAAO,IAAI,CAAC;gBAChB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,CAAC;YACd,CAAC;QACH,KAAK,OAAO;YACV,OAAO,KAAK,CAAC,KAAK,CAAC;QACrB,KAAK,YAAY;YAEf,QAAQ,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;gBACzB,KAAK,KAAK;oBACR,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC;gBACjC,KAAK,MAAM;oBAET,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;gBAC1C,KAAK,MAAM;oBACT,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;gBAC3B;oBACE,KAAK,CAAC,KAAqB,CAAC;YAChC,CAAC;QACH,KAAK,SAAS,CAAC;QACf,KAAK,SAAS,CAAC;QACf,KAAK,OAAO,CAAC;QACb,KAAK,KAAK;YACR,OAAO,IAAI,CAAC;QACd;YACE,KAAqB,CAAC;IAC1B,CAAC;AACH,CAAC;AAED,SAAS,SAAS,CAChB,OAAmE,EACnE,GAAuB,EACvB,QAA0B;IAE1B,MAAM,KAAK,GAAG,oBAAoB,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAE5D,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAChD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;QACrB,KAAK,QAAQ;YACX,OAAO,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACvE,KAAK,OAAO;YACV,OAAO,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACtE,KAAK,YAAY;YACf,OAAO,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,yBAAU,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;QACnE;YACE,OAAO,KAAK,CAAC;IACjB,CAAC;AACH,CAAC;AAED,SAAS,cAAc,CACrB,UAAkC,EAClC,GAAwC,EACxC,KAAc,EACd,QAA0B;IAE1B,GAAG,GAAG,MAAM,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IACpC,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAExC,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,OAAO,KAAK,CAAC;IAC5C,QAAQ,UAAU,EAAE,CAAC;QACnB,KAAK,OAAO;YACV,OAAO,GAAG,KAAK,KAAK,CAAC;QACvB,KAAK,cAAc;YACjB,OAAO,GAAG,GAAG,KAAK,CAAC;QACrB,KAAK,oBAAoB;YACvB,OAAO,GAAG,IAAI,KAAK,CAAC;QACtB,KAAK,WAAW;YACd,OAAO,GAAG,GAAG,KAAK,CAAC;QACrB,KAAK,iBAAiB;YACpB,OAAO,GAAG,GAAG,KAAK,CAAC;IACvB,CAAC;AACH,CAAC;AAED,SAAS,WAAW,CAClB,OAAqE,EACrE,QAA0B;IAE1B,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;QACrB,KAAK,wBAAwB;YAC3B,OAAO,8CAAqB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACrD,KAAK,KAAK;YACR,OAAO,0BAAW,CAAC,KAAK,KAAK,KAAK,CAAC;QACrC,KAAK,KAAK;YACR,OAAO,0BAAW,CAAC,KAAK,CAAC;IAC7B,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,MAAM,CAAI,KAAgC,EAAE,MAAe;IAClE,OAAO,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAI,KAAK;QACzD,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;QACnB,CAAC,CAAE,KAAW,CAAC;AACnB,CAAC;AAED,SAAS,cAAc,CACrB,IAAU,EACV,QAAyB,EACzB,UAAgC;IAEhC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;QACnC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,MAAM,SAAS,GACb,SAAS,CAAC,IAAI,KAAK,gBAAgB;YACjC,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;YACtC,CAAC,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAE9B,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;YAChC,MAAM,SAAS,GACb,SAAS,CAAC,IAAI,KAAK,gBAAgB;gBACjC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC/C,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAEvC,OAAO,SAAS,KAAK,SAAS,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC;YACzC,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,aAAa,CAAC,SAAc,EAAE,SAA6B;IAClE,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;IAEtC,IAAI,SAAS,IAAI,IAAI;QAAE,OAAO,SAAS,IAAI,IAAI,CAAC;IAEhD,QAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC;QAE3B,KAAK,OAAO,CAAC,CAAC,CAAC;YAEb,OAAO,SAAS,IAAI,IAAI,IAAI,SAAS,IAAI,EAAE,CAAC;QAC9C,CAAC;QACD,KAAK,QAAQ,CAAC,CAAC,CAAC;YAEd,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC;QAC5B,CAAC;QAED,KAAK,YAAY,CAAC;QAClB,KAAK,QAAQ,CAAC;QACd,KAAK,WAAW,CAAC;QACjB,KAAK,QAAQ;YACX,OAAO,KAAK,CAAC;QACf,KAAK,UAAU;YACb,OAAO,SAAS,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACzD,KAAK,OAAO,CAAC,CAAC,CAAC;YACb,OAAO,SAAS,EAAE,QAAQ,EAAE,IAAI,SAAS,CAAC,KAAK,CAAC;QAClD,CAAC;IACH,CAAC;AACH,CAAC"}