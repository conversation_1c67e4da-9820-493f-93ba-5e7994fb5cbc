import React from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  StyleProp,
} from 'react-native';
import { Text } from './Text';

export interface CardProps {
  title?: string;
  subtitle?: string;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
  children?: React.ReactNode;
}

export const Card = ({
  title,
  subtitle,
  onPress,
  style,
  children,
}: CardProps) => {
  const CardContainer = onPress ? TouchableOpacity : View;

  return (
    <CardContainer
      style={[
        styles.card,
        style,
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {(title || subtitle) && (
        <View style={styles.header}>
          {title && (
            <Text variant="h3" style={styles.title}>
              {title}
            </Text>
          )}
          {subtitle && (
            <Text variant="body2" style={styles.subtitle}>
              {subtitle}
            </Text>
          )}
        </View>
      )}
      {children && (
        <View style={styles.content}>
          {children}
        </View>
      )}
    </CardContainer>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    padding: 16,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    marginBottom: 12,
  },
  title: {
    marginBottom: 4,
  },
  subtitle: {
    color: '#6B7280',
  },
  content: {
    flex: 1,
  },
});

export default Card;