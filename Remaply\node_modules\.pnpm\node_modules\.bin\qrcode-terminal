#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/qrcode-terminal@0.11.0/node_modules/qrcode-terminal/bin/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/qrcode-terminal@0.11.0/node_modules/qrcode-terminal/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/qrcode-terminal@0.11.0/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/qrcode-terminal@0.11.0/node_modules/qrcode-terminal/bin/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/qrcode-terminal@0.11.0/node_modules/qrcode-terminal/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/qrcode-terminal@0.11.0/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../qrcode-terminal/bin/qrcode-terminal.js" "$@"
else
  exec node  "$basedir/../qrcode-terminal/bin/qrcode-terminal.js" "$@"
fi
