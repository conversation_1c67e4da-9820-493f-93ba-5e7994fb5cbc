{"version": 3, "file": "react-native-safe-area-context.native.js", "sourceRoot": "", "sources": ["../../../src/runtime/third-party-libs/react-native-safe-area-context.native.tsx"], "names": [], "mappings": ";;AAoBA,kEAQC;AA5BD,iCAQe;AACf,+CAAwC;AAExC,6CAAyE;AAOzE,IAAI,oBAA+C,CAAC;AAEpD,SAAgB,2BAA2B,CAAC,IAAwB;IAClE,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC;IAC3C,IAAI,uBAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK,kBAAkB,EAAE,CAAC;QACzD,oBAAoB,KAAK,WAAW,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,GAAG,oBAAoB,CAAC;IAC9B,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,WAAW,CAAC,IAAwB;IAC3C,SAAS,WAAW,CAAC,EAAE,QAAQ,EAAyB;QACtD,IAAI,CAAC;YAQH,MAAM,EACJ,iBAAiB,EACjB,gBAAgB,GACjB,GAAG,OAAO,CAAC,gCAAgC,CAAC,CAAC;YAY9C,IAAI,IAAI,KAAK,gBAAgB,EAAE,CAAC;gBAC9B,OAAO,IAAA,qBAAa,EAAC,gBAAQ,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;YAC/C,CAAC;YAED,MAAM,MAAM,GAAG,iBAAiB,EAAE,CAAC;YACnC,MAAM,gBAAgB,GAAG,IAAA,kBAAU,EAAC,wBAAe,CAAC,CAAC;YAErD,MAAM,UAAU,GACd,gBAAgB,YAAY,GAAG;gBAC7B,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;gBAChD,CAAC,CAAC,gBAAgB,CAAC;YAEvB,MAAM,KAAK,GAAG,IAAA,eAAO,EACnB,GAAG,EAAE,CAAC,CAAC;gBACL,GAAG,UAAU;gBACb,2CAA2C,EAAE,MAAM,CAAC,MAAM;gBAC1D,yCAAyC,EAAE,MAAM,CAAC,IAAI;gBACtD,0CAA0C,EAAE,MAAM,CAAC,KAAK;gBACxD,wCAAwC,EAAE,MAAM,CAAC,GAAG;aACrD,CAAC,EACF,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAC3B,CAAC;YAEF,OAAO,IAAA,qBAAa,EAAC,wBAAe,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,EAAE,QAAQ,CAAC,CAAC;QACtE,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAA,qBAAa,EAAC,gBAAQ,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,OAAO,SAAS,oBAAoB,CAAC,EACnC,QAAQ,EACR,GAAG,KAAK,EACc;QACtB,OAAO,IAAA,qBAAa,EAAC,IAAI,EAAE,KAAK,EAAE,IAAA,qBAAa,EAAC,WAAW,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC9E,CAAC,CAAC;AACJ,CAAC"}