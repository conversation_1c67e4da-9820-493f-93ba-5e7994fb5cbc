{"version": 3, "file": "render-component.js", "sourceRoot": "", "sources": ["../../../src/runtime/native/render-component.tsx"], "names": [], "mappings": ";;;AAsBA,0CAwKC;AA9LD,iCAAqD;AACrD,+CAAyC;AAEzC,uCAA6C;AAC7C,qCAA2C;AAG3C,MAAM,aAAa,GAAG,IAAI,GAAG,EAG1B,CAAC;AAES,QAAA,YAAY,GAAG;IAC1B,IAAI,EAAE,CAAC;IACP,cAAc,EAAE,CAAC;IACjB,QAAQ,EAAE,CAAC;IACX,MAAM,EAAE,CAAC;CACV,CAAC;AAKF,SAAgB,eAAe,CAC7B,aAAiC,EACjC,KAAkB,EAClB,KAA0B,EAC1B,qBAA0C,EAC1C,SAA+B,EAC/B,UAAgC;IAEhC,IAAI,SAAS,GAAG,aAAa,CAAC;IAC9B,MAAM,UAAU,GAAG,KAAK,CAAC,cAAc,CAAC;IAExC,IAAI,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC;QAChD,OAAO,CAAC,GAAG,CACT,+BAA+B,KAAK,EAAE,MAAM,QAAQ,IAAI,CAAC,SAAS,CAChE;YACE,aAAa,EAAE,KAAK,CAAC,aAAa;YAClC,KAAK,EAAE,KAAK,CAAC,QAAQ;gBACnB,CAAC,CAAC;oBACE,GAAG,KAAK;oBACR,GAAG,qBAAqB;iBACzB;gBACH,CAAC,CAAC,EAAE,GAAG,KAAK,EAAE,GAAG,qBAAqB,EAAE;YAC1C,SAAS;YACT,UAAU;SACX,EACD,gBAAgB,EAAE,EAClB,CAAC,CACF,EAAE,CACJ,CAAC;IACJ,CAAC;IAGD,IAAI,KAAK,CAAC,SAAS,KAAK,oBAAY,CAAC,IAAI,EAAE,CAAC;QAC1C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,IAAI,UAAU,IAAI,KAAK,CAAC,SAAS,KAAK,oBAAY,CAAC,cAAc,EAAE,CAAC;gBAClE,mBAAmB,CACjB,oSAAoS,EACpS,KAAK,CAAC,aAAa,CACpB,CAAC;YACJ,CAAC;QACH,CAAC;QACD,SAAS,GAAG,wBAAS,CAAC;QACtB,KAAK,CAAC,SAAS,GAAG,oBAAY,CAAC,QAAQ,CAAC;QACxC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,IAAI,KAAK,CAAC,QAAQ,KAAK,oBAAY,CAAC,IAAI,EAAE,CAAC;QACzC,IAAI,UAAU,IAAI,KAAK,CAAC,QAAQ,KAAK,oBAAY,CAAC,cAAc,EAAE,CAAC;YACjE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;gBAC1C,mBAAmB,CACjB,8RAA8R,EAC9R,KAAK,CAAC,aAAa,CACpB,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,QAAQ,GAAG,oBAAY,CAAC,QAAQ,CAAC;YACvC,SAAS,GAAG,uBAAuB,CAAC,SAAS,CAAC,CAAC;YAE/C,MAAM,EAAE,gBAAgB,EAAE,GACxB,OAAO,CAAC,yBAAyB,CAA6C,CAAC;YAEjF,KAAK,CAAC,KAAK,GAAG,gBAAgB,CAAC,GAAG,EAAE;gBAClC,SAAS,oBAAoB,CAAC,KAAU;oBAEtC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK;wBAAE,OAAO,KAAK,CAAC;oBAEtD,IAAI,0BAA0B,IAAI,KAAK,IAAI,OAAO,IAAI,KAAK,EAAE,CAAC;wBAC5D,OAAO,KAAK,CAAC,KAAK,CAAC;oBACrB,CAAC;oBACD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;wBAAE,OAAO,KAAK,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;oBACjE,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAM,EAAE,EAAE;wBAC9C,OAAO,CAAC,GAAG,EAAE,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC5C,CAAC,CAAC,CACH,CAAC;gBACJ,CAAC;gBAED,IAAI,CAAC;oBACH,OAAO,oBAAoB,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;gBACjE,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,OAAO,CAAC,GAAG,CAAC,sBAAsB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;oBACnD,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC,EAAE,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;SAAM,CAAC;QACN,KAAK,GAAG,EAAE,GAAG,KAAK,EAAE,GAAG,qBAAqB,EAAE,CAAC;IACjD,CAAC;IAED,IAAI,KAAK,CAAC,SAAS,KAAK,oBAAY,CAAC,IAAI,EAAE,CAAC;QAC1C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,IAAI,UAAU,IAAI,KAAK,CAAC,SAAS,KAAK,oBAAY,CAAC,cAAc,EAAE,CAAC;gBAClE,mBAAmB,CACjB,kQAAkQ,EAClQ,KAAK,CAAC,aAAa,CACpB,CAAC;YACJ,CAAC;QACH,CAAC;QACD,KAAK,CAAC,SAAS,GAAG,oBAAY,CAAC,QAAQ,CAAC;QAExC,KAAK,GAAG;YACN,KAAK,EAAE,SAAS;YAChB,QAAQ,EAAE,IAAA,qBAAa,EAAC,SAAS,EAAE,KAAK,CAAC;SAC1C,CAAC;QACF,SAAS,GAAG,wBAAe,CAAC,QAAQ,CAAC;IACvC,CAAC;IAED,IAAI,KAAK,CAAC,UAAU,KAAK,oBAAY,CAAC,IAAI,EAAE,CAAC;QAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,IAAI,UAAU,IAAI,KAAK,CAAC,UAAU,KAAK,oBAAY,CAAC,cAAc,EAAE,CAAC;gBACnE,mBAAmB,CACjB,+RAA+R,EAC/R,KAAK,CAAC,aAAa,CACpB,CAAC;YACJ,CAAC;QACH,CAAC;QACD,KAAK,CAAC,UAAU,GAAG,oBAAY,CAAC,QAAQ,CAAC;QAEzC,KAAK,GAAG;YACN,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,IAAA,qBAAa,EAAC,SAAS,EAAE,KAAK,CAAC;SAC1C,CAAC;QACF,SAAS,GAAG,0BAAgB,CAAC,QAAQ,CAAC;IACxC,CAAC;IAGD,KAAK,CAAC,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC;IAwC7D,OAAO,IAAA,qBAAa,EAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAEzC,CAAC;AAED,SAAS,uBAAuB,CAAC,SAA6B;IAC5D,IAAI,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;QACjC,OAAO,aAAa,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;IACvC,CAAC;SAAM,IAAI,SAAS,CAAC,WAAW,EAAE,UAAU,CAAC,mBAAmB,CAAC,EAAE,CAAC;QAClE,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IACE,CAAC,CACC,OAAO,SAAS,KAAK,UAAU;QAC/B,CAAC,SAAS,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAC9D,EACD,CAAC;QACD,MAAM,IAAI,KAAK,CACb,0EAA0E,SAAS,CAAC,IAAI,uGAAuG,CAChM,CAAC;IACJ,CAAC;IAED,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GACzB,OAAO,CAAC,yBAAyB,CAA6C,CAAC;IAEjF,MAAM,iBAAiB,GAAG,QAAQ,CAAC,uBAAuB,CACxD,SAAiC,CAClC,CAAC;IACF,iBAAiB,CAAC,WAAW,GAAG,YAAY,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,IAAI,IAAI,SAAS,EAAE,CAAC;IAEnG,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;IAEhD,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED,SAAS,mBAAmB,CAC1B,OAAe,EACf,aAAqD;IAErD,OAAO,CAAC,GAAG,CACT,kCAAkC,OAAO,gEAAgE,SAAS,CAAC,aAAa,CAAC,wMAAwM,CAC1U,CAAC;AACJ,CAAC;AAED,SAAS,SAAS,CAAC,MAAW;IAC5B,MAAM,IAAI,GAAG,IAAI,OAAO,EAAE,CAAC;IAC3B,OAAO,IAAI,CAAC,SAAS,CACnB,MAAM,EACN,SAAS,OAAO,CAAC,CAAC,EAAE,KAAK;QACvB,IAAI,CAAC,CAAC,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,EAAE,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACpB,OAAO,YAAY,CAAC;QACtB,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAEhB,MAAM,QAAQ,GAAQ,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAErD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1C,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAEnB,OAAO,QAAQ,CAAC;IAClB,CAAC,EACD,CAAC,CACF,CAAC;AACJ,CAAC;AAiBD,SAAS,gBAAgB;IACvB,MAAM,IAAI,GAAG,IAAI,OAAO,EAAU,CAAC;IACnC,OAAO,CAAC,CAAS,EAAE,KAAc,EAAE,EAAE;QACnC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YAChD,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;gBACpB,OAAO,YAAY,CAAC;YACtB,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAEhB,IAAI,0BAA0B,IAAI,KAAK,IAAI,OAAO,IAAI,KAAK,EAAE,CAAC;gBAC5D,OAAO,GAAG,KAAK,CAAC,KAAK,mBAAmB,CAAC;YAC3C,CAAC;YAED,IAAI,KAAK,IAAI,KAAK,IAAI,OAAO,KAAK,CAAC,GAAG,KAAK,UAAU,EAAE,CAAC;gBACtD,OAAO,KAAK,CAAC,GAAG,EAAE,CAAC;YACrB,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YACvC,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC;QAChE,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;AACJ,CAAC"}