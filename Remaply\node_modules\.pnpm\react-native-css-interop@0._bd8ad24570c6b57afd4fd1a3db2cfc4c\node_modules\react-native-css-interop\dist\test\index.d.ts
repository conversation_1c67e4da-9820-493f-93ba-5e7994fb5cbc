import { ComponentProps, ComponentRef, ForwardedRef, ReactElement } from "react";
import { RenderOptions as TLRenderOptions } from "@testing-library/react-native";
import { isReduceMotionEnabled } from "../runtime/native/appearance-observables";
import { vh, vw } from "../runtime/native/unit-observables";
import { CssToReactNativeRuntimeOptions, EnableCssInteropOptions, ReactComponent, Style } from "../types";
export * from "../index";
export * from "../runtime/native/styles";
export * from "../types";
export * from "@testing-library/react-native";
export { INTERNAL_SET } from "../shared";
declare global {
    namespace jest {
        interface Matchers<R> {
            toHaveStyle(style?: Style | Style[]): R;
            toHaveAnimatedStyle(style?: Style): R;
        }
    }
}
export declare const native: {
    vw: typeof vw;
    vh: typeof vh;
    isReduceMotionEnabled: typeof isReduceMotionEnabled;
};
export interface RenderOptions extends TLRenderOptions {
    css?: string;
    cssOptions?: CssToReactNativeRuntimeOptions;
    debugCompiled?: boolean;
}
export declare function render(component: ReactElement<any>, { css, cssOptions, debugCompiled, ...options }?: RenderOptions): {
    update: (component: React.ReactElement) => void;
    unmount: () => void;
    rerender: (component: React.ReactElement) => void;
    toJSON: () => null | import("react-test-renderer").ReactTestRendererJSON | import("react-test-renderer").ReactTestRendererJSON[];
    debug: import("@testing-library/react-native").DebugFunction;
    root: ReactTestInstance;
    UNSAFE_root: ReactTestInstance;
    UNSAFE_getByProps: (props: {
        [key: string]: any;
    }) => ReactTestInstance;
    UNSAFE_getAllByProps: (props: {
        [key: string]: any;
    }) => Array<ReactTestInstance>;
    UNSAFE_queryByProps: (props: {
        [key: string]: any;
    }) => ReactTestInstance | null;
    UNSAFE_queryAllByProps: (props: {
        [key: string]: any;
    }) => Array<ReactTestInstance>;
    UNSAFE_getByType: <P>(type: React.ComponentType<P>) => ReactTestInstance;
    UNSAFE_getAllByType: <P>(type: React.ComponentType<P>) => Array<ReactTestInstance>;
    UNSAFE_queryByType: <P>(type: React.ComponentType<P>) => ReactTestInstance | null;
    UNSAFE_queryAllByType: <P>(type: React.ComponentType<P>) => Array<ReactTestInstance>;
    getByA11yValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryByA11yValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findByA11yValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getByA11yState: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryByA11yState: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findByA11yState: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getByRole: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
    getAllByRole: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
    queryByRole: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
    queryAllByRole: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
    findByRole: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
    findAllByRole: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
    getByHintText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByHintText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByHintText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByHintText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByHintText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByHintText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getByA11yHint: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByA11yHint: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByA11yHint: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getByLabelText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByLabelText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByLabelText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getByTestId: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByTestId: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByTestId: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByTestId: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByTestId: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByTestId: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getByText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
};
export declare namespace render {
    var debug: (component: ReactElement<any>, options?: RenderOptions) => {
        update: (component: React.ReactElement) => void;
        unmount: () => void;
        rerender: (component: React.ReactElement) => void;
        toJSON: () => null | import("react-test-renderer").ReactTestRendererJSON | import("react-test-renderer").ReactTestRendererJSON[];
        debug: import("@testing-library/react-native").DebugFunction;
        root: ReactTestInstance;
        UNSAFE_root: ReactTestInstance;
        UNSAFE_getByProps: (props: {
            [key: string]: any;
        }) => ReactTestInstance;
        UNSAFE_getAllByProps: (props: {
            [key: string]: any;
        }) => Array<ReactTestInstance>;
        UNSAFE_queryByProps: (props: {
            [key: string]: any;
        }) => ReactTestInstance | null;
        UNSAFE_queryAllByProps: (props: {
            [key: string]: any;
        }) => Array<ReactTestInstance>;
        UNSAFE_getByType: <P>(type: React.ComponentType<P>) => ReactTestInstance;
        UNSAFE_getAllByType: <P>(type: React.ComponentType<P>) => Array<ReactTestInstance>;
        UNSAFE_queryByType: <P>(type: React.ComponentType<P>) => ReactTestInstance | null;
        UNSAFE_queryAllByType: <P>(type: React.ComponentType<P>) => Array<ReactTestInstance>;
        getByA11yValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryByA11yValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findByA11yValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getByA11yState: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryByA11yState: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findByA11yState: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getByRole: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        getAllByRole: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        queryByRole: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        queryAllByRole: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        findByRole: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        findAllByRole: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        getByHintText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByHintText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByHintText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByHintText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByHintText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByHintText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByA11yHint: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByA11yHint: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByA11yHint: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByLabelText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByLabelText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByLabelText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByTestId: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByTestId: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByTestId: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByTestId: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByTestId: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByTestId: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    };
}
export declare function getWarnings(): Map<string, import("../types").ExtractionWarning[]>;
export declare function createMockComponent<const C extends ReactComponent<any>, const M extends EnableCssInteropOptions<C>>(Component: C, mapping: M & EnableCssInteropOptions<C>): import("react").ForwardRefExoticComponent<import("react").PropsWithoutRef<ComponentProps<C>> & import("react").RefAttributes<ComponentRef<C>>> & {
    mock: jest.Mock<import("react").ReactNode, [ComponentProps<C>, ref: ForwardedRef<ComponentRef<C>>], any>;
};
export declare function createRemappedComponent<const C extends ReactComponent<any>, const M extends EnableCssInteropOptions<C>>(Component: C, mapping: M & EnableCssInteropOptions<C>): import("react").ForwardRefExoticComponent<import("react").PropsWithoutRef<ComponentProps<C>> & import("react").RefAttributes<ComponentRef<C>>> & {
    mock: jest.Mock<import("react").ReactNode, [ComponentProps<C>, ref: ForwardedRef<ComponentRef<C>>], any>;
};
export declare const resetComponents: () => void;
export declare function registerCSS(css: string, { debugCompiled, ...options }?: CssToReactNativeRuntimeOptions & {
    debugCompiled?: boolean;
}): void;
export declare namespace registerCSS {
    var debug: (css: string, options?: CssToReactNativeRuntimeOptions) => void;
    var noDebug: (css: string, options?: CssToReactNativeRuntimeOptions) => void;
}
export declare const testID = "react-native-css-interop";
export declare function setupAllComponents(): void;
