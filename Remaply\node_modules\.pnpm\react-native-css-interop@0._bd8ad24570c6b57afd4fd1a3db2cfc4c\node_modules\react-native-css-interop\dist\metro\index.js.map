{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/metro/index.ts"], "names": [], "mappings": ";;;;;AAyFA,wCASC;AAlGD,4CAAoB;AACpB,2DAAoC;AACpC,gDAAwB;AAExB,sDAA8B;AAC9B,iCAAmD;AAKnD,4CAAuD;AAEvD,iCAAgD;AA+DhD,IAAI,KAAU,CAAC;AACf,IAAI,sBAAsB,GAA8B,SAAS,CAAC;AAClE,MAAM,cAAc,GAAG,IAAI,GAAG,EAAoC,CAAC;AACnE,MAAM,eAAe,GAAG,cAAI,CAAC,OAAO,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;AAChE,MAAM,UAAU,GAAG,2BAA2B,IAAI,OAAO,CAAC,GAAG,CAAC;AAU9D,SAAgB,cAAc,CAC5B,MAAkD,EAClD,OAA8B;IAE9B,OAAO,OAAO,MAAM,KAAK,UAAU;QACjC,CAAC,CAAC,KAAK,UAAU,cAAc;YAC3B,OAAO,SAAS,CAAC,MAAM,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;QAC5C,CAAC;QACH,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AACjC,CAAC;AAED,SAAS,SAAS,CAChB,MAAmB,EACnB,OAA8B;IAE9B,MAAM,KAAK,GAAG,IAAA,aAAO,EAAC,OAAO,CAAC,MAAM,EAAE,IAAI,IAAI,0BAA0B,CAAC,CAAC;IAC1E,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACxB,KAAK,CAAC,mBAAmB,eAAe,EAAE,CAAC,CAAC;IAC5C,KAAK,CAAC,eAAe,UAAU,EAAE,CAAC,CAAC;IAEnC,IAAA,6BAAsB,GAAE,CAAC;IAEzB,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,EAAE,cAAc,CAAC;IACzD,MAAM,2BAA2B,GAAG,MAAM,CAAC,WAAW,EAAE,mBAAmB,CAAC;IAC5E,MAAM,kBAAkB,GAAG,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC;IAG5D,MAAM,cAAc,GAAG,uBAAuB,CAAC;IAE/C,YAAE,CAAC,SAAS,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IACnD,YAAE,CAAC,aAAa,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;IAC1C,YAAE,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;IAC9C,YAAE,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;IAC7C,YAAE,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;IAC5C,YAAE,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;IAE9C,OAAO;QACL,GAAG,MAAM;QACT,eAAe,EAAE,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC;QACjD,WAAW,EAAE;YACX,GAAG,MAAM,CAAC,WAAW;YACrB,GAAG;gBACD,0BAA0B,EAAE,MAAM,CAAC,eAAe;gBAClD,0BAA0B,EAAE,cAAI,CAAC,QAAQ,CACvC,OAAO,CAAC,GAAG,EAAE,EACb,eAAe,CAChB;aACF;YACD,KAAK,CAAC,mBAAmB,CACvB,WAAW,EACX,gBAAgB,EAChB,iBAAiB;gBAEjB,KAAK,CAAC,2BAA2B,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC;gBACzD,KAAK,CAAC,gCAAgC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACnE,KAAK,CACH,8CAA8C,OAAO,CAAC,sBAAsB,CAAC,EAAE,CAChF,CAAC;gBAEF,MAAM,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,IAAI,QAAQ,CAAC;gBACvD,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAExC,IAAI,sBAAsB,EAAE,CAAC;oBAC3B,MAAM,sBAAsB,CAAC;oBAC7B,MAAM,iBAAiB,CACrB,QAAQ,EACR,QAAQ,EACR,gBAAgB,CAAC,GAAG,EACpB,OAAO,EACP,KAAK,CACN,CAAC;gBACJ,CAAC;gBAGD,MAAM,iBAAiB,GACrB,CAAC,sBAAsB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;gBAEnD,KAAK,CAAC,yCAAyC,iBAAiB,EAAE,CAAC,CAAC;gBAEpE,IAAI,iBAAiB,EAAE,CAAC;oBACtB,KAAK,CAAC,8BAA8B,QAAQ,EAAE,CAAC,CAAC;oBAGhD,MAAM,OAAO,GAAG,UAAU;wBACxB,CAAC,CAAC,KAAK,EAAE,GAAW,EAAE,EAAE;4BACpB,MAAM,MAAM,GACV,QAAQ,KAAK,KAAK;gCAChB,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE;gCAChB,CAAC,CAAC,WAAW,CACT,IAAA,mCAAuB,EAAC,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC,EAC5C,KAAK,CACN,CAAC;4BAER,MAAM,kBAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;wBAC9C,CAAC;wBACH,CAAC,CAAC,SAAS,CAAC;oBAEd,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;oBAE/D,MAAM,MAAM,GACV,QAAQ,KAAK,KAAK;wBAChB,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC;wBACvB,CAAC,CAAC,WAAW,CACT,IAAA,mCAAuB,EAAC,GAAG,EAAE,OAAO,EAAE,KAAK,CAAC,EAC5C,KAAK,CACN,CAAC;oBAER,MAAM,kBAAS,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;oBAC5D,MAAM,kBAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;oBAC5C,IAAI,QAAQ,KAAK,KAAK,EAAE,CAAC;wBACvB,MAAM,kBAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;oBACnE,CAAC;oBAED,KAAK,CAAC,8BAA8B,CAAC,CAAC;gBACxC,CAAC;gBAED,OAAO,MAAM,CAAC,MAAM,CAClB,EAAE,EACF,MAAM,2BAA2B,EAAE,CACjC,WAAW,EACX,gBAAgB,EAChB,iBAAiB,CAClB,CACF,CAAC;YACJ,CAAC;SACF;QACD,MAAM,EAAE;YACN,GAAG,MAAM,CAAC,MAAM;YAChB,iBAAiB,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,EAAE;gBAC7C,KAAK,CAAC,yBAAyB,CAAC,CAAC;gBACjC,MAAM,MAAM,GAAG,IAAA,iBAAO,GAAE,CAAC;gBACzB,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC,UAAU,EAAE,CAAC;gBAEtD,IAAI,OAAO,CAAC,oBAAoB,EAAE,CAAC;oBACjC,KAAK,CAAC,2BAA2B,CAAC,CAAC;gBACrC,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,UAAU,EAAE,CAAC;wBAChB,sBAAsB,GAAG,OAAO;6BAC7B,kBAAkB,EAAE;6BACpB,IAAI,CAAC,KAAK,EAAE,KAAU,EAAE,EAAE;4BACzB,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;4BACrB,uBAAuB,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;4BAC3C,oBAAoB,CAAC,OAAO,CAAC,CAAC;wBAChC,CAAC,CAAC,CAAC;wBAEL,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE;4BAE/B,MAAM,sBAAsB,CAAC;4BAC7B,IAAI,EAAE,CAAC;wBACT,CAAC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAED,OAAO,kBAAkB;oBACvB,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;oBACzD,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC7B,CAAC;SACF;QACD,QAAQ,EAAE;YACR,GAAG,MAAM,CAAC,QAAQ;YAClB,UAAU,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC;YAC5D,cAAc,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,EAAE;gBAChD,IAAI,UAAU,KAAK,cAAc,EAAE,CAAC;oBAClC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;gBAC3B,CAAC;gBAED,MAAM,QAAQ,GAAG,gBAAgB,IAAI,OAAO,CAAC,cAAc,CAAC;gBAC5D,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;gBAGzD,IAAI,CAAC,CAAC,UAAU,IAAI,QAAQ,IAAI,QAAQ,CAAC,QAAQ,KAAK,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBACrE,OAAO,QAAQ,CAAC;gBAClB,CAAC;gBAED,QAAQ,GAAG,QAAQ,IAAI,QAAQ,CAAC;gBAGhC,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;gBAGxC,MAAM,WAAW,GAAI,OAAe,CAAC,KAAK,IAAK,OAAe,CAAC,GAAG,CAAC;gBACnE,MAAM,eAAe,GAAG,CAAC,WAAW,IAAI,QAAQ,KAAK,KAAK,CAAC;gBAE3D,KAAK,CAAC,wBAAwB,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACnD,KAAK,CAAC,8BAA8B,QAAQ,EAAE,CAAC,CAAC;gBAChD,KAAK,CAAC,+BAA+B,WAAW,EAAE,CAAC,CAAC;gBACpD,KAAK,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;gBAE9C,IAAI,sBAAsB,IAAI,CAAC,eAAe,EAAE,CAAC;oBAC/C,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;gBACrE,CAAC;gBAED,OAAO;oBACL,GAAG,QAAQ;oBACX,QAAQ;iBACT,CAAC;YACJ,CAAC;SACF;KACF,CAAC;AACJ,CAAC;AAED,KAAK,UAAU,iBAAiB,CAC9B,QAAgB,EAChB,QAAgB,EAChB,KAAc,EACd,EAAE,KAAK,EAAE,iBAAiB,EAAE,GAAG,OAAO,EAAyB,EAC/D,KAAe;IAGf,IAAI,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;QACjC,OAAO;IACT,CAAC;IAED,KAAK,CAAC,kBAAkB,QAAQ,EAAE,CAAC,CAAC;IACpC,KAAK,CAAC,wBAAwB,KAAK,EAAE,CAAC,CAAC;IACvC,KAAK,CAAC,uBAAuB,cAAc,CAAC,IAAI,EAAE,CAAC,CAAC;IAEpD,OAAO,GAAG;QACR,KAAK,EAAE;YACL,SAAS,EAAE,IAAI,GAAG,EAAE;YACpB,KAAK,EAAE,IAAI,GAAG,EAAE;YAChB,aAAa,EAAE,EAAE;YACjB,kBAAkB,EAAE,EAAE;SACvB;QACD,GAAG,OAAO;KACX,CAAC;IAEF,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,KAAK,CAAC,qCAAqC,CAAC,CAAC;QAC7C,cAAc,CAAC,GAAG,CAChB,QAAQ,EACR,iBAAiB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YACvC,OAAO,QAAQ,KAAK,KAAK;gBACvB,CAAC,CAAC,GAAG;gBACL,CAAC,CAAC,WAAW,CAAC,IAAA,mCAAuB,EAAC,GAAG,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,KAAK,CAAC,oCAAoC,CAAC,CAAC;QAC5C,cAAc,CAAC,GAAG,CAChB,QAAQ,EACR,iBAAiB,CAAC,QAAQ,EAAE,CAAC,GAAW,EAAE,EAAE;YAC1C,KAAK,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;YAE1C,cAAc,CAAC,GAAG,CAChB,QAAQ,EACR,OAAO,CAAC,OAAO,CACb,QAAQ,KAAK,KAAK;gBAChB,CAAC,CAAC,GAAG;gBACL,CAAC,CAAC,WAAW,CAAC,IAAA,mCAAuB,EAAC,GAAG,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,CAC9D,CACF,CAAC;YAEF,KAAK,CAAC,sBAAsB,QAAQ,EAAE,CAAC,CAAC;YACxC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACnB,WAAW,EAAE;oBACX;wBACE,QAAQ;wBACR,QAAQ,EAAE;4BACR,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;4BACxB,IAAI,EAAE,CAAC;4BACP,IAAI,EAAE,SAAS;yBAChB;wBACD,IAAI,EAAE,QAAQ;qBACf;iBACF;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;YACd,KAAK,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC;YAC3C,OAAO,QAAQ,KAAK,KAAK;gBACvB,CAAC,CAAC,GAAG;gBACL,CAAC,CAAC,WAAW,CAAC,IAAA,mCAAuB,EAAC,GAAG,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;AACH,CAAC;AAKD,SAAS,uBAAuB,CAC9B,EAIC;IAED,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;QACtC,MAAM,gBAAgB,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7C,EAAE,CAAC,OAAO,GAAG,CAAC,QAAQ,EAAE,EAAE;YACxB,IAAI,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAEjC,OAAO,GAAG,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;YACrC,CAAC;YACD,OAAO,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC;QACF,EAAE,CAAC,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC;IAC1C,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAKD,SAAS,oBAAoB,CAC3B,OAEC;IAED,IAAI,OAAO,CAAC,aAAa,CAAC,sBAAsB,EAAE,CAAC;QACjD,OAAO;IACT,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE1D,OAAO,CAAC,aAAa,GAAG,KAAK,WAC3B,QAAQ,EACR,gBAAgB,EAChB,UAAU;QAEV,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEnD,IAAI,aAAa,EAAE,CAAC;YAClB,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,aAAa,CAAC,CAAC;QAChD,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,EAAE,gBAAgB,EAAE,UAAU,CAAC,CAAC;IAC/D,CAAC,CAAC;IACF,OAAO,CAAC,aAAa,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACtD,CAAC;AAED,SAAS,WAAW,CAAC,IAAI,GAAG,EAAE,EAAE,KAAe;IAC7C,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACzB,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC;IACtC,KAAK,CAAC,gBAAgB,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;IACjE,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,YAAY,CAAC,QAAQ,GAAG,QAAQ;IACvC,OAAO,cAAI,CAAC,IAAI,CACd,eAAe,EACf,GAAG,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CACnD,CAAC;AACJ,CAAC;AAMD,SAAS,SAAS,CAAC,IAAa;IAC9B,QAAQ,OAAO,IAAI,EAAE,CAAC;QACpB,KAAK,QAAQ,CAAC;QACd,KAAK,QAAQ,CAAC;QACd,KAAK,UAAU;YACb,MAAM,IAAI,KAAK,CAAC,oBAAoB,OAAO,IAAI,EAAE,CAAC,CAAC;QACrD,KAAK,QAAQ;YACX,OAAO,IAAI,IAAI,GAAG,CAAC;QACrB,KAAK,QAAQ;YAEX,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC;QAC7C,KAAK,SAAS;YACZ,OAAO,GAAG,IAAI,EAAE,CAAC;QACnB,KAAK,WAAW;YAGd,OAAO,MAAM,CAAC;QAChB,KAAK,QAAQ,CAAC,CAAC,CAAC;YACd,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;gBAClB,OAAO,MAAM,CAAC;YAChB,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/B,OAAO,IAAI,IAAI;qBACZ,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;oBAIb,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;wBAC1C,CAAC,CAAC,EAAE;wBACJ,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACvB,CAAC,CAAC;qBACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;YAClB,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;qBAC5B,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;oBAExB,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;wBAC1C,OAAO,EAAE,CAAC;oBACZ,CAAC;oBAGD,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC;wBAC3B,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC;oBACnB,CAAC;oBAED,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;oBAEzB,OAAO,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC;gBAC7B,CAAC,CAAC;qBACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;YAClB,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC"}