@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\node-gyp-build@4.8.4\node_modules\node-gyp-build\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\node-gyp-build@4.8.4\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\node-gyp-build@4.8.4\node_modules\node-gyp-build\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\node-gyp-build@4.8.4\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\node-gyp-build\build-test.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\node-gyp-build\build-test.js" %*
)
