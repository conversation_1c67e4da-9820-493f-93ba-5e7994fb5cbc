"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThemeProvider = exports.useTheme = void 0;
const react_1 = __importStar(require("react"));
const themes_1 = require("./themes");
const ThemeContext = (0, react_1.createContext)(undefined);
const useTheme = () => {
    const context = (0, react_1.useContext)(ThemeContext);
    if (!context) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
};
exports.useTheme = useTheme;
const ThemeProvider = ({ children }) => {
    console.log('🔍 DETECTIVE: ThemeProvider component is rendering!');
    const [isDark, setIsDark] = (0, react_1.useState)(() => {
        console.log('🔍 DETECTIVE: ThemeProvider useState initializer running');
        try {
            // Check if we're in a browser environment
            if (typeof window === 'undefined' || typeof localStorage === 'undefined') {
                console.log('🔍 DETECTIVE: Not in browser environment, defaulting to light theme');
                return false;
            }
            const savedTheme = localStorage.getItem('theme');
            console.log('🔍 DETECTIVE: Saved theme from localStorage:', savedTheme);
            const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
            console.log('🔍 DETECTIVE: System prefers dark:', prefersDark);
            const result = savedTheme === 'dark' || (!savedTheme && prefersDark);
            console.log('🔍 DETECTIVE: Final isDark result:', result);
            return result;
        }
        catch (error) {
            console.error('🔍 DETECTIVE: Error in ThemeProvider useState:', error);
            return false; // Default to light theme
        }
    });
    console.log('🔍 DETECTIVE: ThemeProvider isDark state:', isDark);
    const toggleTheme = () => {
        console.log('🔍 DETECTIVE: toggleTheme called');
        setIsDark(prev => !prev);
    };
    (0, react_1.useEffect)(() => {
        console.log('🔍 DETECTIVE: ThemeProvider useEffect running, isDark:', isDark);
        try {
            if (typeof localStorage !== 'undefined') {
                localStorage.setItem('theme', isDark ? 'dark' : 'light');
                console.log('🔍 DETECTIVE: Theme saved to localStorage:', isDark ? 'dark' : 'light');
            }
        }
        catch (error) {
            console.error('🔍 DETECTIVE: Error saving theme to localStorage:', error);
        }
    }, [isDark]);
    const value = {
        theme: isDark ? themes_1.darkTheme : themes_1.lightTheme,
        isDark,
        toggleTheme
    };
    console.log('🔍 DETECTIVE: ThemeProvider about to render children');
    return (<ThemeContext.Provider value={value}>
            {children}
        </ThemeContext.Provider>);
};
exports.ThemeProvider = ThemeProvider;
//# sourceMappingURL=ThemeProvider.js.map