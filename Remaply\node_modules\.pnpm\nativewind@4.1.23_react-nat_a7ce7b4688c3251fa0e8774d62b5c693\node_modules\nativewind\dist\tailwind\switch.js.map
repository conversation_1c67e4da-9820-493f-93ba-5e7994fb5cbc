{"version": 3, "file": "switch.js", "sourceRoot": "", "sources": ["../../src/tailwind/switch.ts"], "names": [], "mappings": ";;;;;;AAAA,mGAA2E;AAC3E,qFAA6D;AAC7D,+FAAuE;AACvE,gEAAwC;AAE3B,QAAA,YAAY,GAAG,IAAA,gBAAM,EAAC,UAAU,EAAE,cAAc,EAAE,KAAK,EAAE;IACpE,cAAc,CACZ;QACE,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;YACf,OAAO;gBACL,GAAG,EAAE;oBACH,iCAAiC,EAAE,MAAM;oBACzC,aAAa,EAAE,IAAA,sBAAY,EAAC,KAAK,CAAC;iBACnC;aACF,CAAC;QACJ,CAAC;KACF,EACD;QACE,MAAM,EAAE,IAAA,6BAAmB,EAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAChD,IAAI,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC;KACvB,CACF,CAAC;AACJ,CAAC,CAAC,CAAC;AAEU,QAAA,SAAS,GAAG,IAAA,gBAAM,EAAC,UAAU,EACxC,cAAc,EACd,KAAK,EACL,WAAW,GACZ;IACC,cAAc,CACZ;QACE,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE;YACf,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBACtC,OAAO;oBACL,sBAAsB,EAAE;wBACtB,eAAe,EAAE,IAAA,sBAAY,EAAC,KAAK,CAAC,GAAG,aAAa;qBACrD;iBACF,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,IAAA,2BAAiB,EAAC;gBAC/B,KAAK,EAAE,KAAK;gBACZ,QAAQ,EAAE,kBAAkB;gBAC5B,QAAQ,EAAE,iBAAiB;aAC5B,CAAC,CAAC;YAEH,MAAM,CAAC,kBAAkB,CAAC,IAAI,aAAa,CAAC;YAE5C,OAAO;gBACL,sBAAsB,EAAE,MAAM;aAC/B,CAAC;QACJ,CAAC;KACF,EACD;QACE,MAAM,EAAE,IAAA,6BAAmB,EAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAChD,IAAI,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC;KACvB,CACF,CAAC;AACJ,CAAC,CAAC,CAAC"}