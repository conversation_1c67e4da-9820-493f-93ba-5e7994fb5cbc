#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/@expo+cli@0.24.20_bufferutil@4.0.9/node_modules/@expo/cli/build/bin/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/@expo+cli@0.24.20_bufferutil@4.0.9/node_modules/@expo/cli/build/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/@expo+cli@0.24.20_bufferutil@4.0.9/node_modules/@expo/cli/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/@expo+cli@0.24.20_bufferutil@4.0.9/node_modules/@expo/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/@expo+cli@0.24.20_bufferutil@4.0.9/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/@expo+cli@0.24.20_bufferutil@4.0.9/node_modules/@expo/cli/build/bin/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/@expo+cli@0.24.20_bufferutil@4.0.9/node_modules/@expo/cli/build/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/@expo+cli@0.24.20_bufferutil@4.0.9/node_modules/@expo/cli/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/@expo+cli@0.24.20_bufferutil@4.0.9/node_modules/@expo/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/@expo+cli@0.24.20_bufferutil@4.0.9/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@expo/cli/build/bin/cli" "$@"
else
  exec node  "$basedir/../@expo/cli/build/bin/cli" "$@"
fi
