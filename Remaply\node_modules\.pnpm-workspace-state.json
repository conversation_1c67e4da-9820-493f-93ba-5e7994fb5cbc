{"lastValidatedTimestamp": 1752704969459, "projects": {"C:\\Users\\<USER>\\Desktop\\projectX\\Remaply": {"name": "remaply-monorepo", "version": "1.0.0"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\backend": {"name": "@remaply/backend", "version": "1.0.0"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\frontend-shared": {"name": "@remaply/frontend-shared", "version": "1.0.0"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\mobile": {"name": "@remaply/mobile", "version": "1.0.0"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\packages\\api": {"name": "@remaply/api", "version": "0.1.0"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\packages\\auth": {"name": "@remaply/auth", "version": "0.1.0"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\packages\\db": {"name": "@remaply/db", "version": "0.1.0"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\packages\\ui": {"name": "@remaply/ui", "version": "0.1.0"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\packages\\validators": {"name": "@remaply/validators", "version": "0.1.0"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\shared": {"name": "@remaply/shared", "version": "1.0.0"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\tooling\\eslint": {"name": "@remaply/eslint-config", "version": "0.3.0"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\tooling\\github": {"name": "@remaply/github"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\tooling\\prettier": {"name": "@remaply/prettier-config", "version": "0.1.0"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\tooling\\tailwind": {"name": "@remaply/tailwind-config", "version": "0.1.0"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\tooling\\typescript": {"name": "@remaply/tsconfig", "version": "0.1.0"}, "C:\\Users\\<USER>\\Desktop\\projectX\\Remaply\\web": {"name": "web", "version": "1.0.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {"default": {"eslint": "^9.28.0", "prettier": "^3.5.3", "tailwindcss": "^3.4.15", "typescript": "^5.8.3", "zod": "^3.25.49", "@tanstack/react-query": "^5.80.7", "@trpc/client": "^11.4.0", "@trpc/tanstack-react-query": "^11.4.0", "@trpc/server": "^11.4.0"}, "react19": {"react": "19.0.0", "react-dom": "19.0.0", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5"}}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["shared", "frontend-shared", "mobile", "web", "backend", "packages/*", "tooling/*"]}, "filteredInstall": false}