import React from 'react';
import {
  View,
  Dimensions,
  ScrollView,
  StatusBar,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Button, Text } from '@remaply/frontend-shared';
import { OnboardingScreenProps } from './types';
import { useOnboardingScreen } from './useOnboardingScreen';

const { width } = Dimensions.get('window');

const OnboardingScreen: React.FC<OnboardingScreenProps> = ({ navigation }) => {
  const {
    currentIndex,
    slides,
    scrollViewRef,
    handleScroll,
    handleNext,
    handleSkip,
  } = useOnboardingScreen();

  const renderSlide = (slide: any, index: number) => (
    <View key={index} className="w-screen flex-1 justify-center items-center px-8">
      <View className="items-center max-w-80">
        {/* Visual */}
        <View className="w-30 h-30 rounded-full bg-white/15 justify-center items-center mb-10 border-2 border-white/30 shadow-lg">
          <Text className="text-5xl">{slide.visual}</Text>
        </View>

        {/* Title */}
        <Text variant="h1" className="text-3xl font-bold text-white text-center leading-9 mb-4">
          {slide.title}
        </Text>

        {/* Subtitle */}
        <Text variant="body1" className="text-lg text-white/90 text-center leading-6 font-medium mb-4">
          {slide.subtitle}
        </Text>

        {/* Description */}
        <Text variant="body2" className="text-sm text-white/70 text-center leading-5">
          {slide.description}
        </Text>
      </View>
    </View>
  );

  const renderIndicators = () => (
    <View className="flex-row justify-center items-center py-5">
      {slides.map((_, index) => (
        <View
          key={index}
          className={`h-2 rounded-full mx-1 ${
            index === currentIndex 
              ? 'bg-white w-6' 
              : 'bg-white/30 w-2'
          }`}
        />
      ))}
    </View>
  );

  return (
    <View className="flex-1">
      <StatusBar
        barStyle="light-content"
        backgroundColor="transparent"
        translucent
      />

      <LinearGradient
        colors={['#6C63FF', '#5A52E0', '#4A90E2']}
        className="flex-1"
      >
        {/* Header */}
        <View className="flex-row justify-end px-6 pt-15 pb-5">
          <TouchableOpacity onPress={handleSkip}>
            <Text className="text-white/80 text-base font-medium">Skip</Text>
          </TouchableOpacity>
        </View>

        {/* Slides */}
        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleScroll}
          scrollEventThrottle={16}
          className="flex-1"
        >
          {slides.map(renderSlide)}
        </ScrollView>

        {/* Indicators */}
        {renderIndicators()}

        {/* Footer */}
        <View className="px-8 pb-12">
          <Button
            variant="primary"
            size="large"
            fullWidth
            onPress={handleNext}
            style={{ borderRadius: 12, shadowColor: '#6C63FF', shadowOffset: { width: 0, height: 4 }, shadowOpacity: 0.3, shadowRadius: 8, elevation: 8 }}
            children={currentIndex === slides.length - 1 ? 'Get Started' : 'Continue'}
          />
        </View>
      </LinearGradient>
    </View>
  );
};

export default OnboardingScreen; 