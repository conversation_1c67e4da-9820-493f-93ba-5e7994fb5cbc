import React from 'react';
import {
  View,
  Dimensions,
  ScrollView,
  StatusBar,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Button, Text } from '@remaply/frontend-shared';
import { OnboardingScreenProps } from './types';
import { useOnboardingScreen } from './useOnboardingScreen';

const { width } = Dimensions.get('window');

const OnboardingScreen: React.FC<OnboardingScreenProps> = ({ navigation }) => {
  const {
    currentIndex,
    slides,
    scrollViewRef,
    handleScroll,
    handleNext,
    handleSkip,
  } = useOnboardingScreen();

  const renderSlide = (slide: any, index: number) => (
    <View key={index} style={[styles.slide, { width }]}>
      <View style={styles.slideContent}>
        {/* Visual */}
        <View style={styles.visualContainer}>
          <Text style={styles.visualEmoji}>{slide.visual}</Text>
        </View>

        {/* Title */}
        <Text style={styles.title}>
          {slide.title}
        </Text>

        {/* Subtitle */}
        <Text style={styles.subtitle}>
          {slide.subtitle}
        </Text>

        {/* Description */}
        <Text style={styles.description}>
          {slide.description}
        </Text>
      </View>
    </View>
  );

  const renderIndicators = () => (
    <View style={styles.indicatorContainer}>
      {slides.map((_, index) => (
        <View
          key={index}
          style={[
            styles.indicator,
            index === currentIndex ? styles.indicatorActive : styles.indicatorInactive,
          ]}
        />
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor="#FFFFFF"
        translucent={false}
      />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleSkip}>
          <Text style={styles.skipText}>Skip</Text>
        </TouchableOpacity>
      </View>

      {/* Slides */}
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        style={styles.scrollView}
      >
        {slides.map(renderSlide)}
      </ScrollView>

      {/* Indicators */}
      {renderIndicators()}

      {/* Footer */}
      <View style={styles.footer}>
        <Button
          variant="primary"
          size="large"
          fullWidth
          onPress={handleNext}
          style={styles.button}
          children={currentIndex === slides.length - 1 ? 'Get Started' : 'Continue'}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF", // Pure white background
  },
  header: {
    flexDirection: "row",
    justifyContent: "flex-end",
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 20,
  },
  skipText: {
    color: "#6E6E6E", // Medium gray
    fontSize: 16,
    fontWeight: "500",
    fontFamily: "'Avenir Next', 'Helvetica Neue', sans-serif",
  },
  scrollView: {
    flex: 1,
  },
  slide: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 32,
  },
  slideContent: {
    alignItems: "center",
    maxWidth: 320,
  },
  visualContainer: {
    width: 140,
    height: 140,
    borderRadius: 20,
    backgroundColor: "#F5F5F0", // Cream white
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 40,
    borderWidth: 1,
    borderColor: "#E0E0E0",
    shadowColor: "#2C2C2C",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  visualEmoji: {
    fontSize: 56,
  },
  title: {
    fontSize: 32,
    fontWeight: "800",
    color: "#1A1A1A", // Very dark gray
    textAlign: "center",
    lineHeight: 40,
    marginBottom: 16,
    fontFamily: "Georgia, 'Times New Roman', serif",
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 18,
    color: "#4A4A4A", // Dark medium gray
    textAlign: "center",
    lineHeight: 26,
    fontWeight: "500",
    marginBottom: 16,
    fontFamily: "'Avenir Next', 'Helvetica Neue', sans-serif",
  },
  description: {
    fontSize: 15,
    color: "#6E6E6E", // Medium gray
    textAlign: "center",
    lineHeight: 22,
    fontFamily: "'Helvetica Neue', Arial, sans-serif",
    fontWeight: "400",
  },
  indicatorContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 20,
  },
  indicator: {
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  indicatorActive: {
    backgroundColor: "#2C2C2C", // Dark gray
    width: 24,
  },
  indicatorInactive: {
    backgroundColor: "#E0E0E0", // Light gray
    width: 8,
  },
  footer: {
    paddingHorizontal: 32,
    paddingBottom: 50,
  },
  button: {
    backgroundColor: "#2C2C2C", // Dark gray
    borderRadius: 16,
    shadowColor: "#2C2C2C",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
});

export default OnboardingScreen;