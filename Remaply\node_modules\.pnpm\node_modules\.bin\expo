#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/expo@53.0.19_@babel+core@7._854767902230fb6bf5cfcde7a7b35923/node_modules/expo/bin/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/expo@53.0.19_@babel+core@7._854767902230fb6bf5cfcde7a7b35923/node_modules/expo/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/expo@53.0.19_@babel+core@7._854767902230fb6bf5cfcde7a7b35923/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/expo@53.0.19_@babel+core@7._854767902230fb6bf5cfcde7a7b35923/node_modules/expo/bin/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/expo@53.0.19_@babel+core@7._854767902230fb6bf5cfcde7a7b35923/node_modules/expo/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/expo@53.0.19_@babel+core@7._854767902230fb6bf5cfcde7a7b35923/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../expo/bin/cli" "$@"
else
  exec node  "$basedir/../expo/bin/cli" "$@"
fi
