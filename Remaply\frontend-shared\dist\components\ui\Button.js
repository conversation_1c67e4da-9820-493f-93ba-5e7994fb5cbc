"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Button = void 0;
const react_1 = __importDefault(require("react"));
const react_native_1 = require("react-native");
const ThemeProvider_1 = require("../../theme/ThemeProvider");
const Text_1 = require("./Text");
// Remove React.FC and export as function component directly
const Button = ({ variant = 'primary', size = 'medium', onPress, disabled = false, loading = false, fullWidth = false, style, textStyle, children, }) => {
    const { theme } = (0, ThemeProvider_1.useTheme)();
    const getVariantStyles = () => {
        switch (variant) {
            case 'primary':
                return {
                    backgroundColor: theme.colors.primary.main,
                    borderWidth: 0,
                };
            case 'secondary':
                return {
                    backgroundColor: theme.colors.secondary.main,
                    borderWidth: 0,
                };
            case 'outline':
                return {
                    backgroundColor: 'transparent',
                    borderWidth: 1,
                    borderColor: theme.colors.primary.main,
                };
            case 'text':
                return {
                    backgroundColor: 'transparent',
                    borderWidth: 0,
                };
            default:
                return {};
        }
    };
    const getSizeStyles = () => {
        switch (size) {
            case 'small':
                return {
                    paddingVertical: theme.spacing.xs,
                    paddingHorizontal: theme.spacing.sm,
                };
            case 'medium':
                return {
                    paddingVertical: theme.spacing.sm,
                    paddingHorizontal: theme.spacing.md,
                };
            case 'large':
                return {
                    paddingVertical: theme.spacing.md,
                    paddingHorizontal: theme.spacing.lg,
                };
            default:
                return {};
        }
    };
    const getTextColor = () => {
        if (disabled)
            return theme.colors.text.disabled;
        if (variant === 'outline' || variant === 'text')
            return theme.colors.primary.main;
        return theme.colors.primary.contrast;
    };
    return (<react_native_1.TouchableOpacity onPress={onPress} disabled={disabled || loading} style={[
            styles.button,
            getVariantStyles(),
            getSizeStyles(),
            fullWidth && styles.fullWidth,
            disabled && styles.disabled,
            style,
        ]}>
      {loading ? (<react_native_1.ActivityIndicator color={getTextColor()} size="small"/>) : (<Text_1.Text variant="button" style={[
                { color: getTextColor() },
                textStyle,
            ]}>
          {children}
        </Text_1.Text>)}
    </react_native_1.TouchableOpacity>);
};
exports.Button = Button;
const styles = react_native_1.StyleSheet.create({
    button: {
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
    },
    fullWidth: {
        width: '100%',
    },
    disabled: {
        opacity: 0.5,
    },
});
exports.default = exports.Button;
//# sourceMappingURL=Button.js.map