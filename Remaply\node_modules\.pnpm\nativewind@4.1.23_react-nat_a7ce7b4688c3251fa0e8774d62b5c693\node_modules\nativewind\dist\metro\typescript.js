"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupTypeScript = setupTypeScript;
const fs_1 = require("fs");
const comment_json_1 = require("comment-json");
const picocolors_1 = require("./picocolors");
function setupTypeScript(envPath) {
    try {
        const ts = require("typescript");
        if (!ts) {
            return;
        }
        const configFileName = ts.findConfigFile("./", ts.sys.fileExists, "tsconfig.json");
        const userConfig = (0, comment_json_1.parse)((0, fs_1.readFileSync)(configFileName, {
            encoding: "utf-8",
        }));
        if (typeof userConfig !== "object" ||
            !userConfig ||
            Array.isArray(userConfig)) {
            return;
        }
        const output = [];
        let updatedConfig = false;
        if (!(0, fs_1.existsSync)(envPath)) {
            (0, fs_1.writeFileSync)(envPath, `/// <reference types="nativewind/types" />

// NOTE: This file should not be edited and should be committed with your source code. It is generated by NativeWind.`);
            output.push(`Created ${(0, picocolors_1.cyan)(envPath)}`);
        }
        userConfig.include ??= new comment_json_1.CommentArray(envPath);
        if (Array.isArray(userConfig.include) &&
            !userConfig.include.includes(envPath)) {
            userConfig.include.push(envPath);
            updatedConfig = true;
            output.push(`Updated ${configFileName} to include the ${(0, picocolors_1.cyan)(envPath)} file`);
        }
        if (updatedConfig) {
            (0, fs_1.writeFileSync)(configFileName, (0, comment_json_1.stringify)(userConfig, null, 2));
        }
        if (output.length) {
            console.log(`${(0, picocolors_1.cyan)((0, picocolors_1.bold)("NativeWind"))} made the following changes to your project to support TypeScript:\n  - ${output.join("\n  - ")}`);
        }
    }
    catch { }
}
//# sourceMappingURL=typescript.js.map