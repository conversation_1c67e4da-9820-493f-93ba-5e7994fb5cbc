{"name": "@remaply/mobile", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "expo start", "start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo export", "clean": "rm -rf .expo node_modules"}, "dependencies": {"@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "@remaply/frontend-shared": "workspace:*", "@remaply/shared": "workspace:*", "expo": "~53.0.17", "expo-linear-gradient": "~14.1.5", "expo-status-bar": "~2.2.3", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "tailwindcss": "catalog:"}, "private": true}