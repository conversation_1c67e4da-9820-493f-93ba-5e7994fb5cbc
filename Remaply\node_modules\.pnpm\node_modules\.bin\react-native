#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/react-native@0.79.5_@babel+_97f2afdfd421e82989650950ccb96049/node_modules/react-native/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/react-native@0.79.5_@babel+_97f2afdfd421e82989650950ccb96049/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/react-native@0.79.5_@babel+_97f2afdfd421e82989650950ccb96049/node_modules/react-native/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/react-native@0.79.5_@babel+_97f2afdfd421e82989650950ccb96049/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../react-native/cli.js" "$@"
else
  exec node  "$basedir/../react-native/cli.js" "$@"
fi
