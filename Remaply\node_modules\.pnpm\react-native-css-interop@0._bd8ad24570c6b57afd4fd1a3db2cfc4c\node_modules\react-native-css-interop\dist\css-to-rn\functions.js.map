{"version": 3, "file": "functions.js", "sourceRoot": "", "sources": ["../../src/css-to-rn/functions.ts"], "names": [], "mappings": ";;AAEA,sCAEC;AAED,wCAQC;AAED,4CAQC;AAED,0CAQC;AAED,gCAEC;AAED,8BAEC;AAED,8DAEC;AAED,kDAEC;AAED,sCAUC;AA5DD,SAAgB,aAAa;IAC3B,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED,SAAgB,cAAc,CAC5B,SAA+D;IAE/D,IAAI,MAAM,GAAa,EAAE,CAAC;IAC1B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;QACrD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC;IACjC,CAAC;IACD,OAAO,kBAAkB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AAC/C,CAAC;AAED,SAAgB,gBAAgB,CAC9B,SAAuD;IAEvD,IAAI,MAAM,GAAa,EAAE,CAAC;IAC1B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;QACrD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC;IACjC,CAAC;IACD,OAAO,oBAAoB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AACjD,CAAC;AAED,SAAgB,eAAe,CAC7B,SAAuD;IAEvD,IAAI,MAAM,GAAa,EAAE,CAAC;IAC1B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;QACrD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC;IACjC,CAAC;IACD,OAAO,mBAAmB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AAChD,CAAC;AAED,SAAgB,UAAU,CAAC,KAAc;IACvC,OAAO,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,KAAK,GAAG,CAAC;AACvE,CAAC;AAED,SAAgB,SAAS,CAAC,KAAc;IACtC,OAAO,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,KAAK,GAAG,CAAC;AACrE,CAAC;AAED,SAAgB,yBAAyB,CAAC,KAAa;IACrD,OAAO,6BAA6B,KAAK,GAAG,CAAC;AAC/C,CAAC;AAED,SAAgB,mBAAmB,CAAC,KAAa;IAC/C,OAAO,uBAAuB,KAAK,GAAG,CAAC;AACzC,CAAC;AAED,SAAgB,aAAa,CAAC,GAAG,MAAgB;IAC/C,OAAO,iBAAiB,MAAM;SAC3B,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QAEb,OAAO,KAAK;aACT,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC;aACtB,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC;aACtB,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAC5B,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AAClB,CAAC"}