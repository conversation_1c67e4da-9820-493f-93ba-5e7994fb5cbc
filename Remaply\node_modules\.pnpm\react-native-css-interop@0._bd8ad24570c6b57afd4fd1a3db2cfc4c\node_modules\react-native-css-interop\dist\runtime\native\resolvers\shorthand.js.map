{"version": 3, "file": "shorthand.js", "sourceRoot": "", "sources": ["../../../../src/runtime/native/resolvers/shorthand.ts"], "names": [], "mappings": ";;AAkBA,4CA6DC;AA/ED,4CAAoD;AACpD,oDAAiD;AAEjD,qCAA2C;AAe3C,SAAgB,gBAAgB,CAC9B,QAAoC,EACpC,QAAiC;IAEjC,MAAM,SAAS,GAAuB,CACpC,OAAO,EACP,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,KAAK,EACL,EAAE;QACF,IAAI,CAAC,IAAA,0BAAiB,EAAC,UAAU,CAAC;YAAE,OAAO;QAE3C,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC5C,OAAO,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;YACtC,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBAClC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBACpB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAE9B,QAAQ,IAAI,EAAE,CAAC;oBACb,KAAK,QAAQ,CAAC;oBACd,KAAK,QAAQ;wBACX,OAAO,OAAO,KAAK,KAAK,IAAI,CAAC;oBAC/B,KAAK,OAAO;wBACV,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;oBAChE,KAAK,QAAQ;wBACX,OAAO,OAAO,KAAK,KAAK,QAAQ;4BAC9B,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC;4BACrB,CAAC,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC;gBAClC,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEvC,MAAM,MAAM,GAAyB;YACnC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;gBAC1B,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACrB,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC3B,CAAC;gBACD,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAU,CAAC;YAC5C,CAAC,CAAC;YACF,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;gBACtC,MAAM,KAAK,GAAG,6BAAa,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,OAAO;oBACL,GAAG,CAAC,CAAC,CAAC;oBACN,OAAO,KAAK,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK;iBACpD,CAAC;YACb,CAAC,CAAC;SACH,CAAC;QAEF,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,wBAAe,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;IAC5D,CAAC,CAAC;IAEF,OAAO,SAAS,CAAC;AACnB,CAAC"}