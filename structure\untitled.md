droid Bundled 4025ms mobile\index.js (1535 modules)
 LOG  🔍 DETECTIVE: ThemeProvider component is rendering!
 LOG  🔍 DETECTIVE: ThemeProvider useState initializer running
 LOG  🔍 DETECTIVE: Not in browser environment, defaulting to light theme
 LOG  🔍 DETECTIVE: ThemeProvider isDark state: false
 LOG  🔍 DETECTIVE: ThemeProvider about to render children
 LOG  🔍 DETECTIVE: ThemeProvider component is rendering!
 LOG  🔍 DETECTIVE: ThemeProvider useState initializer running
 LOG  🔍 DETECTIVE: Not in browser environment, defaulting to light theme
 LOG  🔍 DETECTIVE: ThemeProvider isDark state: false
 LOG  🔍 DETECTIVE: ThemeProvider about to render children
 ERROR  Warning: Error: Exception in HostFunction: Unable to convert string to floating point value: "large"

This error is located at:

Call Stack
  RNSScreenStack (<anonymous>)
  RNCSafeAreaProvider (<anonymous>)
  NativeStackView (<anonymous>)
  App (<anonymous>)
