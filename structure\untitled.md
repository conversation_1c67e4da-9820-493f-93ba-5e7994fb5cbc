droid Bundled 4025ms mobile\index.js (1535 modules)
 LOG  🔍 DETECTIVE: ThemeProvider component is rendering!
 LOG  🔍 DETECTIVE: ThemeProvider useState initializer running
 LOG  🔍 DETECTIVE: Not in browser environment, defaulting to light theme
 LOG  🔍 DETECTIVE: ThemeProvider isDark state: false
 LOG  🔍 DETECTIVE: ThemeProvider about to render children
 LOG  🔍 DETECTIVE: ThemeProvider component is rendering!
 LOG  🔍 DETECTIVE: ThemeProvider useState initializer running
 LOG  🔍 DETECTIVE: Not in browser environment, defaulting to light theme
 LOG  🔍 DETECTIVE: ThemeProvider isDark state: false
 LOG  🔍 DETECTIVE: ThemeProvider about to render children
 ERROR  Warning: Error: Exception in HostFunction: Unable to convert string to floating point value: "large"

This error is located at:

Call Stack
  RNSScreenStack (<anonymous>)
  RNCSafeAreaProvider (<anonymous>)
  NativeStackView (<anonymous>)
  App (<anonymous>)


🔍 DETECTIVE: ThemeProvider component is rendering!
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:19 🔍 DETECTIVE: ThemeProvider useState initializer running
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:28 🔍 DETECTIVE: Saved theme from localStorage: dark
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:31 🔍 DETECTIVE: System prefers dark: true
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:34 🔍 DETECTIVE: Final isDark result: true
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:43 🔍 DETECTIVE: ThemeProvider isDark state: true
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:68 🔍 DETECTIVE: ThemeProvider about to render children
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:51 🔍 DETECTIVE: ThemeProvider useEffect running, isDark: true
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:55 🔍 DETECTIVE: Theme saved to localStorage: dark

🔍 DETECTIVE: ThemeProvider component is rendering!
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:19 🔍 DETECTIVE: ThemeProvider useState initializer running
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:28 🔍 DETECTIVE: Saved theme from localStorage: dark
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:31 🔍 DETECTIVE: System prefers dark: true
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:34 🔍 DETECTIVE: Final isDark result: true
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:43 🔍 DETECTIVE: ThemeProvider isDark state: true
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:68 🔍 DETECTIVE: ThemeProvider about to render children
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:51 🔍 DETECTIVE: ThemeProvider useEffect running, isDark: true
C:\Users\<USER>\Desktop\projectX\Remaply\frontend-shared\src\theme\ThemeProvider.tsx:55 🔍 DETECTIVE: Theme saved to localStorage: dark, ,,,,unable to convert string to floating point value : "large"