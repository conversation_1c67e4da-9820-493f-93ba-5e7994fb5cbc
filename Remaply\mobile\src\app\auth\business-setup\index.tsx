import React from 'react';
import {
  View,
  StatusBar,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Button, Text, Input, Icon } from '@remaply/frontend-shared';
import { BusinessSetupScreenProps } from './types';
import { useBusinessSetupScreen } from './useBusinessSetupScreen';

const BusinessSetupScreen: React.FC<BusinessSetupScreenProps> = ({ navigation }) => {
  const {
    form,
    categories,
    aiTones,
    handleContinue,
    handleBack,
    updateForm,
  } = useBusinessSetupScreen();

  return (
    <View className="flex-1">
      <StatusBar
        barStyle="dark-content"
        backgroundColor="transparent"
        translucent
      />

      <LinearGradient
        colors={['#F9FAFB', '#FFFFFF']}
        className="flex-1"
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          className="flex-1"
        >
          <ScrollView
            contentContainerStyle={{ flexGrow: 1 }}
            showsVerticalScrollIndicator={false}
            className="px-6"
          >
            {/* Header */}
            <View className="flex-row items-center pt-15 pb-6">
              <TouchableOpacity onPress={handleBack} className="p-2">
                <Icon name="back" size={24} color="#374151" />
              </TouchableOpacity>
            </View>

            {/* Welcome Header */}
            <View className="mb-8">
              <Text
                variant="h2"
                className="text-2xl font-bold text-gray-900 mb-2"
                children="Let's set up your business!"
              />
              <Text
                variant="body1"
                className="text-base text-gray-600"
                children="This helps us personalize your experience"
              />
            </View>

            {/* Business Name Input */}
            <View className="mb-6">
              <Text
                variant="body2"
                className="text-sm font-medium text-gray-700 mb-2"
                children="Business Name"
              />
              <Input
                placeholder="Enter your business name"
                value={form.businessName}
                onChangeText={(value) => updateForm('businessName', value)}
                className="bg-white border border-gray-200 rounded-lg"
              />
            </View>

            {/* Business Category */}
            <View className="mb-6">
              <Text
                variant="body2"
                className="text-sm font-medium text-gray-700 mb-3"
                children="Business Category"
              />
              <View className="flex-row flex-wrap gap-2">
                {categories.map((category) => (
                  <TouchableOpacity
                    key={category.id}
                    className={`px-4 py-2 rounded-full border ${
                      form.businessCategory === category.name
                        ? 'bg-indigo-500 border-indigo-500'
                        : 'bg-white border-gray-200'
                    }`}
                    onPress={() => updateForm('businessCategory', category.name)}
                  >
                    <Text
                      variant="body2"
                      className={`text-sm ${
                        form.businessCategory === category.name
                          ? 'text-white font-medium'
                          : 'text-gray-700'
                      }`}
                      children={category.name}
                    />
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* AI Tone Selection */}
            <View className="mb-6">
              <Text
                variant="body2"
                className="text-sm font-medium text-gray-700 mb-2"
                children="AI Tone"
              />
              <Text
                variant="body2"
                className="text-sm text-gray-500 mb-3"
                children="How should AI write your content?"
              />
              <View className="flex-row flex-wrap gap-3">
                {aiTones.map((tone) => (
                  <TouchableOpacity
                    key={tone.id}
                    className={`flex-row items-center px-4 py-3 rounded-lg border ${
                      form.aiTone === tone.name
                        ? 'bg-indigo-500 border-indigo-500'
                        : 'bg-white border-gray-200'
                    }`}
                    onPress={() => updateForm('aiTone', tone.name)}
                  >
                                         <Icon
                       name={tone.icon as any}
                       size={16}
                       color={form.aiTone === tone.name ? '#FFFFFF' : '#9CA3AF'}
                     />
                    <Text
                      variant="body2"
                      className={`ml-2 text-sm ${
                        form.aiTone === tone.name
                          ? 'text-white font-medium'
                          : 'text-gray-700'
                      }`}
                      children={tone.name}
                    />
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Optional Logo Upload */}
            <View className="mb-8">
              <Text
                variant="body2"
                className="text-sm font-medium text-gray-700 mb-3"
                children="Business Logo (Optional)"
              />
              <TouchableOpacity className="border-2 border-dashed border-gray-300 rounded-lg p-6 items-center">
                <Icon name="upload" size={24} color="#9CA3AF" />
                <Text
                  variant="body2"
                  className="text-sm text-gray-500 mt-2"
                  children="Tap to upload logo"
                />
              </TouchableOpacity>
            </View>

            {/* Continue Button */}
            <View className="pb-8">
              <Button
                variant="primary"
                size="large"
                fullWidth
                onPress={handleContinue}
                style={{ borderRadius: 12, shadowColor: '#6C63FF', shadowOffset: { width: 0, height: 4 }, shadowOpacity: 0.3, shadowRadius: 8, elevation: 8 }}
                children="Continue"
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </LinearGradient>
    </View>
  );
};

export default BusinessSetupScreen; 