import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Button, Input, Text } from '@remaply/frontend-shared';
import { useTheme } from '@remaply/frontend-shared';
import { useNavigation } from '@react-navigation/native';

interface BusinessSetupScreenProps {
  navigation: any;
}

const BusinessSetupScreen: React.FC<BusinessSetupScreenProps> = ({ navigation }) => {
  const { theme } = useTheme();
  const nav = useNavigation();

  const [formData, setFormData] = useState({
    businessName: '',
    businessType: '',
    location: '',
    phoneNumber: '',
  });

  const [errors, setErrors] = useState({
    businessName: '',
    businessType: '',
    location: '',
    phoneNumber: '',
  });

  const [isLoading, setIsLoading] = useState(false);

  const businessTypes = [
    'Sole Proprietorship',
    'Partnership',
    'Limited Company',
    'NGO',
    'Cooperative',
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {
      businessName: '',
      businessType: '',
      location: '',
      phoneNumber: '',
    };

    // Business name validation
    if (!formData.businessName.trim()) {
      newErrors.businessName = 'Business name is required';
    } else if (formData.businessName.trim().length < 2) {
      newErrors.businessName = 'Business name must be at least 2 characters';
    }

    // Business type validation
    if (!formData.businessType) {
      newErrors.businessType = 'Please select a business type';
    }

    // Location validation
    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    }

    // Phone number validation (Nigerian format)
    if (!formData.phoneNumber) {
      newErrors.phoneNumber = 'Phone number is required';
    } else if (!/^(\+234|0)[789][01]\d{8}$/.test(formData.phoneNumber.replace(/\s/g, ''))) {
      newErrors.phoneNumber = 'Please enter a valid Nigerian phone number';
    }

    setErrors(newErrors);
    return !Object.values(newErrors).some(error => error);
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      // TODO: Implement actual business setup logic
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      // Navigate to main app
      console.log('Business setup completed');
    } catch (error) {
      console.error('Business setup error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = () => {
    // TODO: Navigate to main app
    console.log('Skipped business setup');
  };

 return (
  <KeyboardAvoidingView
    style={styles.container}
    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
  >
    <ScrollView
      contentContainerStyle={styles.scrollContent}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.header}>
        <Text
          variant="h1"
          style={styles.title}
          children="Business Setup"
        />
        <Text
          variant="body1"
          style={styles.subtitle}
          children="Tell us about your business to personalize your experience"
        />
      </View>

      <View style={styles.form}>
        <Input
          label="Business Name"
          placeholder="Enter your business name"
          value={formData.businessName}
          onChangeText={(value) => handleInputChange('businessName', value)}
          error={errors.businessName}
          autoCapitalize="words"
          autoCorrect={false}
          containerStyle={styles.inputContainer}
        />

        <Input
          label="Business Type"
          placeholder="Select your business type"
          value={formData.businessType}
          onChangeText={(value) => handleInputChange('businessType', value)}
          error={errors.businessType}
          containerStyle={styles.inputContainer}
        />

        <Input
          label="Location"
          placeholder="Enter your business location"
          value={formData.location}
          onChangeText={(value) => handleInputChange('location', value)}
          error={errors.location}
          autoCapitalize="words"
          containerStyle={styles.inputContainer}
        />

        <Input
          label="Phone Number"
          placeholder="Enter your business phone number"
          value={formData.phoneNumber}
          onChangeText={(value) => handleInputChange('phoneNumber', value)}
          error={errors.phoneNumber}
          keyboardType="phone-pad"
          containerStyle={styles.inputContainer}
        />

        <Button
          variant="primary"
          size="medium"
          fullWidth
          onPress={handleSubmit}
          loading={isLoading}
          style={styles.submitButton}
          children={
            <Text
              variant="body2"
              children="Complete Setup"
            />
          }
        />

        <Button
          variant="text"
          onPress={handleSkip}
          style={styles.skipButton}
          children={
            <Text
              variant="body2"
              style={styles.skipText}
              children="Skip for now"
            />
          }
        />
      </View>
    </ScrollView>
  </KeyboardAvoidingView>
);
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  form: {
    flex: 1,
  },
  inputContainer: {
    marginBottom: 20,
  },
  submitButton: {
    marginBottom: 16,
  },
  skipButton: {
    alignSelf: 'center',
  },
  skipText: {
    color: '#6B7280',
  },
});

export default BusinessSetupScreen; 