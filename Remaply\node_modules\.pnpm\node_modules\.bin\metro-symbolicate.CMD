@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\metro-symbolicate@0.82.5\node_modules\metro-symbolicate\src\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\metro-symbolicate@0.82.5\node_modules\metro-symbolicate\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\metro-symbolicate@0.82.5\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\metro-symbolicate@0.82.5\node_modules\metro-symbolicate\src\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\metro-symbolicate@0.82.5\node_modules\metro-symbolicate\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\metro-symbolicate@0.82.5\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\metro-symbolicate\src\index.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\metro-symbolicate\src\index.js" %*
)
