@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\expo-modules-autolinking@2.1.14\node_modules\expo-modules-autolinking\bin\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\expo-modules-autolinking@2.1.14\node_modules\expo-modules-autolinking\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\expo-modules-autolinking@2.1.14\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\expo-modules-autolinking@2.1.14\node_modules\expo-modules-autolinking\bin\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\expo-modules-autolinking@2.1.14\node_modules\expo-modules-autolinking\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\expo-modules-autolinking@2.1.14\node_modules;C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\expo-modules-autolinking\bin\expo-modules-autolinking.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\expo-modules-autolinking\bin\expo-modules-autolinking.js" %*
)
