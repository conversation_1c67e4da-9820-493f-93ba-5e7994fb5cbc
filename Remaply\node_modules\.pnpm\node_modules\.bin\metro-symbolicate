#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/metro-symbolicate@0.82.5/node_modules/metro-symbolicate/src/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/metro-symbolicate@0.82.5/node_modules/metro-symbolicate/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/metro-symbolicate@0.82.5/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/metro-symbolicate@0.82.5/node_modules/metro-symbolicate/src/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/metro-symbolicate@0.82.5/node_modules/metro-symbolicate/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/metro-symbolicate@0.82.5/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../metro-symbolicate/src/index.js" "$@"
else
  exec node  "$basedir/../metro-symbolicate/src/index.js" "$@"
fi
