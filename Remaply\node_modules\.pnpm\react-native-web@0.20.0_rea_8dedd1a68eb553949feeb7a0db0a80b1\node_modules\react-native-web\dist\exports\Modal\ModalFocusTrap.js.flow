/**
 * Copyright (c) <PERSON>.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import * as React from 'react';
import View from '../View';
import createElement from '../createElement';
import StyleSheet from '../StyleSheet';
import UIManager from '../UIManager';
import canUseDOM from '../../modules/canUseDom';

/**
 * This Component is used to "wrap" the modal we're opening
 * so that changing focus via tab will never leave the document.
 *
 * This allows us to properly trap the focus within a modal
 * even if the modal is at the start or end of a document.
 */
declare var FocusBracket: () => any;
declare function attemptFocus(element: any): any;
declare function focusFirstDescendant(element: any): any;
declare function focusLastDescendant(element: any): any;
export type ModalFocusTrapProps = {|
  active?: boolean | (() => boolean),
  children?: any,
|};
declare var ModalFocusTrap: (arg0: ModalFocusTrapProps) => React.Node;
export default ModalFocusTrap;
const styles = StyleSheet.create({
  focusBracket: {
    outlineStyle: 'none'
  }
});