"use client"

import { useState, useEffect, useRef } from "react"
import { Animated, TouchableOpacity } from "react-native"
import Svg, { Circle, Path, Line, Defs, LinearGradient, RadialGradient, Stop, G } from "react-native-svg"

interface NeuralLogoProps {
  size?: number
  interactive?: boolean
}

export default function NeuralLogo({ size = 200, interactive = true }: NeuralLogoProps) {
  const [activeNode, setActiveNode] = useState(0)
  const [isThinking, setIsThinking] = useState(false)
  const [energyLevel, setEnergyLevel] = useState(0)

  // Animated values
  const pulseAnim = useRef(new Animated.Value(1)).current
  const rotationAnim = useRef(new Animated.Value(0)).current
  const glowAnim = useRef(new Animated.Value(0.3)).current

  // Neural network nodes positions
  const nodes = [
    { x: 30, y: 30, size: 6, delay: 0 },
    { x: 170, y: 40, size: 4, delay: 0.5 },
    { x: 180, y: 100, size: 5, delay: 1 },
    { x: 160, y: 160, size: 4, delay: 1.5 },
    { x: 100, y: 180, size: 6, delay: 2 },
    { x: 40, y: 170, size: 4, delay: 2.5 },
    { x: 20, y: 120, size: 5, delay: 3 },
    { x: 25, y: 80, size: 4, delay: 3.5 },
  ]

  // Continuous animations
  useEffect(() => {
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ]),
    )

    const rotationAnimation = Animated.loop(
      Animated.timing(rotationAnim, {
        toValue: 1,
        duration: 20000,
        useNativeDriver: true,
      }),
    )

    const glowAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(glowAnim, {
          toValue: 0.6,
          duration: 3000,
          useNativeDriver: false,
        }),
        Animated.timing(glowAnim, {
          toValue: 0.3,
          duration: 3000,
          useNativeDriver: false,
        }),
      ]),
    )

    pulseAnimation.start()
    rotationAnimation.start()
    glowAnimation.start()

    return () => {
      pulseAnimation.stop()
      rotationAnimation.stop()
      glowAnimation.stop()
    }
  }, [])

  // Node cycling animation
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveNode((prev) => (prev + 1) % 8)
      setEnergyLevel((prev) => (prev + 1) % 100)
    }, 800)
    return () => clearInterval(interval)
  }, [])

  const triggerThinking = () => {
    setIsThinking(true)
    setTimeout(() => setIsThinking(false), 2000)
  }

  const rotation = rotationAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  })

  return (
    <TouchableOpacity
      onPress={interactive ? triggerThinking : undefined}
      activeOpacity={0.8}
      style={{
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Animated.View
        style={{
          transform: [{ scale: pulseAnim }],
          shadowColor: "#14b8a6",
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: glowAnim,
          shadowRadius: 15,
          elevation: 15,
        }}
      >
        <Svg width={size} height={size} viewBox="0 0 200 200">
          <Defs>
            <RadialGradient id="neuralGradient" cx="50%" cy="50%" r="50%">
              <Stop offset="0%" stopColor="#14b8a6" stopOpacity="1" />
              <Stop offset="50%" stopColor="#0f766e" stopOpacity="0.9" />
              <Stop offset="100%" stopColor="#134e4a" stopOpacity="0.7" />
            </RadialGradient>

            <LinearGradient id="energyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#06b6d4" />
              <Stop offset="50%" stopColor="#14b8a6" />
              <Stop offset="100%" stopColor="#10b981" />
            </LinearGradient>
          </Defs>

          {/* Outer energy ring */}
          <Animated.View style={{ transform: [{ rotate: rotation }] }}>
            <Circle
              cx="100"
              cy="100"
              r="90"
              fill="none"
              stroke="url(#energyGradient)"
              strokeWidth="2"
              opacity="0.3"
              strokeDasharray="15,8"
            />
          </Animated.View>

          {/* REVOLUTIONARY "R" DESIGN */}
          <G transform="translate(50, 40)">
            <Path
              d="M20 30 Q15 20 25 20 L70 20 Q85 20 85 35 Q85 50 70 50 L45 50 L80 100 Q85 108 75 112 Q70 108 65 100 L35 55 L30 55 L30 110 Q30 115 25 115 Q20 115 20 110 Z"
              fill="url(#neuralGradient)"
              opacity={isThinking ? 0.9 : 0.8}
            />

            {/* Inner energy core */}
            <Circle
              cx="50"
              cy="45"
              r={6 + Math.sin(energyLevel * 0.2) * 2}
              fill="#06b6d4"
              opacity={0.4 + Math.sin(energyLevel * 0.1) * 0.2}
            />
          </G>

          {/* DYNAMIC NEURAL NODES */}
          {nodes.map((node, i) => (
            <G key={i}>
              <Circle
                cx={node.x + Math.sin(energyLevel * 0.1 + i) * 2}
                cy={node.y + Math.cos(energyLevel * 0.1 + i) * 2}
                r={node.size + (activeNode === i ? 3 : 0)}
                fill={activeNode === i ? "#06b6d4" : "#14b8a6"}
                opacity={activeNode === i ? 1 : 0.7}
              />

              {i < 7 && (
                <Line
                  x1={node.x}
                  y1={node.y}
                  x2={nodes[i + 1].x}
                  y2={nodes[i + 1].y}
                  stroke="#14b8a6"
                  strokeWidth={activeNode === i || activeNode === i + 1 ? "2" : "1"}
                  opacity={activeNode === i || activeNode === i + 1 ? 0.6 : 0.2}
                  strokeDasharray="4,3"
                />
              )}
            </G>
          ))}

          {/* Central energy pulse */}
          <Circle
            cx="100"
            cy="100"
            r={12 + Math.sin(energyLevel * 0.2) * 3}
            fill="none"
            stroke="#14b8a6"
            strokeWidth="1"
            opacity={0.3 + Math.sin(energyLevel * 0.15) * 0.2}
          />
        </Svg>
      </Animated.View>
    </TouchableOpacity>
  )
}
