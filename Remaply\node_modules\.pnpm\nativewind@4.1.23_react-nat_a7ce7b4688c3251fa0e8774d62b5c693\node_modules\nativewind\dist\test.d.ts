import { RenderOptions as InteropRenderOptions } from "react-native-css-interop/test";
import { Config } from "tailwindcss";
export { act, createMockComponent, screen, fireEvent, within, native, INTERNAL_SET, } from "react-native-css-interop/test";
export * from "./index";
export type ConfigWithoutContent = {
    [K in keyof Config as K extends "content" ? never : K]: Config[K];
};
export interface RenderOptions extends InteropRenderOptions {
    config?: ConfigWithoutContent;
    css?: string;
    layers?: {
        base?: boolean;
        components?: boolean;
        utilities?: boolean;
    };
}
export type RenderCurrentTestOptions = RenderOptions & {
    className?: string;
};
export declare function renderCurrentTest({ className, ...options }?: RenderCurrentTestOptions): Promise<{
    props: any;
    invalid: {
        style: Record<string, any>;
        properties: string[];
    } | {
        properties: string[];
        style?: undefined;
    } | {
        style: Record<string, any>;
        properties?: undefined;
    };
} | {
    props: any;
    invalid?: undefined;
}>;
export declare namespace renderCurrentTest {
    var debug: (options?: RenderCurrentTestOptions) => Promise<{
        props: any;
        invalid: {
            style: Record<string, any>;
            properties: string[];
        } | {
            properties: string[];
            style?: undefined;
        } | {
            style: Record<string, any>;
            properties?: undefined;
        };
    } | {
        props: any;
        invalid?: undefined;
    }>;
}
export declare function render(component: React.ReactElement<any>, { config, css, layers, debugCompiled, ...options }?: RenderOptions): Promise<{
    update: (component: React.ReactElement) => void;
    unmount: () => void;
    rerender: (component: React.ReactElement) => void;
    toJSON: () => null | import("react-test-renderer").ReactTestRendererJSON | import("react-test-renderer").ReactTestRendererJSON[];
    debug: import("react-native-css-interop/test").DebugFunction;
    root: ReactTestInstance;
    UNSAFE_root: ReactTestInstance;
    UNSAFE_getByProps: (props: {
        [key: string]: any;
    }) => ReactTestInstance;
    UNSAFE_getAllByProps: (props: {
        [key: string]: any;
    }) => Array<ReactTestInstance>;
    UNSAFE_queryByProps: (props: {
        [key: string]: any;
    }) => ReactTestInstance | null;
    UNSAFE_queryAllByProps: (props: {
        [key: string]: any;
    }) => Array<ReactTestInstance>;
    UNSAFE_getByType: <P>(type: React.ComponentType<P>) => ReactTestInstance;
    UNSAFE_getAllByType: <P>(type: React.ComponentType<P>) => Array<ReactTestInstance>;
    UNSAFE_queryByType: <P>(type: React.ComponentType<P>) => ReactTestInstance | null;
    UNSAFE_queryAllByType: <P>(type: React.ComponentType<P>) => Array<ReactTestInstance>;
    getByA11yValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryByA11yValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findByA11yValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getByA11yState: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryByA11yState: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findByA11yState: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    queryAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    findAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
    getByRole: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
    getAllByRole: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
    queryByRole: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
    queryAllByRole: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
    findByRole: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
    findAllByRole: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
    getByHintText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByHintText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByHintText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByHintText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByHintText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByHintText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getByA11yHint: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByA11yHint: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByA11yHint: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getByLabelText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByLabelText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByLabelText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getByTestId: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByTestId: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByTestId: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByTestId: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByTestId: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByTestId: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getByText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    getAllByText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryByText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    queryAllByText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findByText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    findAllByText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
}>;
export declare namespace render {
    var debug: (component: React.ReactElement<any>, options?: RenderOptions) => Promise<{
        update: (component: React.ReactElement) => void;
        unmount: () => void;
        rerender: (component: React.ReactElement) => void;
        toJSON: () => null | import("react-test-renderer").ReactTestRendererJSON | import("react-test-renderer").ReactTestRendererJSON[];
        debug: import("react-native-css-interop/test").DebugFunction;
        root: ReactTestInstance;
        UNSAFE_root: ReactTestInstance;
        UNSAFE_getByProps: (props: {
            [key: string]: any;
        }) => ReactTestInstance;
        UNSAFE_getAllByProps: (props: {
            [key: string]: any;
        }) => Array<ReactTestInstance>;
        UNSAFE_queryByProps: (props: {
            [key: string]: any;
        }) => ReactTestInstance | null;
        UNSAFE_queryAllByProps: (props: {
            [key: string]: any;
        }) => Array<ReactTestInstance>;
        UNSAFE_getByType: <P>(type: React.ComponentType<P>) => ReactTestInstance;
        UNSAFE_getAllByType: <P>(type: React.ComponentType<P>) => Array<ReactTestInstance>;
        UNSAFE_queryByType: <P>(type: React.ComponentType<P>) => ReactTestInstance | null;
        UNSAFE_queryAllByType: <P>(type: React.ComponentType<P>) => Array<ReactTestInstance>;
        getByA11yValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryByA11yValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findByA11yValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getByA11yState: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryByA11yState: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findByA11yState: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getByRole: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        getAllByRole: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        queryByRole: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        queryAllByRole: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        findByRole: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        findAllByRole: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        getByHintText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByHintText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByHintText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByHintText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByHintText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByHintText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByA11yHint: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByA11yHint: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByA11yHint: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByLabelText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByLabelText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByLabelText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByTestId: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByTestId: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByTestId: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByTestId: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByTestId: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByTestId: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    }>;
    var noDebug: (component: React.ReactElement<any>, options?: RenderOptions) => Promise<{
        update: (component: React.ReactElement) => void;
        unmount: () => void;
        rerender: (component: React.ReactElement) => void;
        toJSON: () => null | import("react-test-renderer").ReactTestRendererJSON | import("react-test-renderer").ReactTestRendererJSON[];
        debug: import("react-native-css-interop/test").DebugFunction;
        root: ReactTestInstance;
        UNSAFE_root: ReactTestInstance;
        UNSAFE_getByProps: (props: {
            [key: string]: any;
        }) => ReactTestInstance;
        UNSAFE_getAllByProps: (props: {
            [key: string]: any;
        }) => Array<ReactTestInstance>;
        UNSAFE_queryByProps: (props: {
            [key: string]: any;
        }) => ReactTestInstance | null;
        UNSAFE_queryAllByProps: (props: {
            [key: string]: any;
        }) => Array<ReactTestInstance>;
        UNSAFE_getByType: <P>(type: React.ComponentType<P>) => ReactTestInstance;
        UNSAFE_getAllByType: <P>(type: React.ComponentType<P>) => Array<ReactTestInstance>;
        UNSAFE_queryByType: <P>(type: React.ComponentType<P>) => ReactTestInstance | null;
        UNSAFE_queryAllByType: <P>(type: React.ComponentType<P>) => Array<ReactTestInstance>;
        getByA11yValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryByA11yValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findByA11yValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findAllByA11yValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findAllByAccessibilityValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-value").AccessibilityValueMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getByA11yState: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryByA11yState: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findByA11yState: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findAllByA11yState: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        queryAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        findAllByAccessibilityState: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/helpers/matchers/match-accessibility-state").AccessibilityStateMatcher, import("@testing-library/react-native/build/queries/options").CommonQueryOptions>;
        getByRole: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        getAllByRole: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        queryByRole: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        queryAllByRole: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        findByRole: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        findAllByRole: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/queries/role").ByRoleMatcher, import("@testing-library/react-native/build/queries/role").ByRoleOptions>;
        getByHintText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByHintText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByHintText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByHintText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByHintText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByHintText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByA11yHint: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByA11yHint: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByA11yHint: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByA11yHint: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByAccessibilityHint: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByLabelText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByLabelText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByLabelText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByLabelText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByPlaceholderText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByDisplayValue: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByTestId: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByTestId: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByTestId: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByTestId: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByTestId: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByTestId: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getByText: import("@testing-library/react-native/build/queries/make-queries").GetByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        getAllByText: import("@testing-library/react-native/build/queries/make-queries").GetAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryByText: import("@testing-library/react-native/build/queries/make-queries").QueryByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        queryAllByText: import("@testing-library/react-native/build/queries/make-queries").QueryAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findByText: import("@testing-library/react-native/build/queries/make-queries").FindByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
        findAllByText: import("@testing-library/react-native/build/queries/make-queries").FindAllByQuery<import("@testing-library/react-native/build/matches").TextMatch, import("@testing-library/react-native/build/queries/options").CommonQueryOptions & import("@testing-library/react-native/build/matches").TextMatchOptions>;
    }>;
}
export declare function invalidProperty(...properties: string[]): {
    type: string;
    property: string;
}[];
export declare function invalidValue(value: Record<string, string>): {
    type: string;
    property: string;
    value: string;
}[];
