{"version": 3, "file": "appearance-observables.js", "sourceRoot": "", "sources": ["../../../src/runtime/native/appearance-observables.ts"], "names": [], "mappings": ";;;AAwDA,sDA2BC;AAnFD,+CAKsB;AAEtB,yCAA8C;AAE9C,8CAAsE;AAKzD,QAAA,iBAAiB,GAAG,IAAA,uBAAU,EACzC,yBAAU,CAAC,cAAc,EAAE,IAAI,OAAO,CACvC,CAAC;AACF,MAAM,qBAAqB,GAAG,IAAA,uBAAU,EACtC,SAAS,CACV,CAAC;AAEW,QAAA,WAAW,GAAG;IACzB,GAAG,CAAC,KAAkC;QACpC,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;YACvB,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC;QAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;YACpC,qBAAqB,CAAC,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IACD,GAAG,CAAC,MAAe;QACjB,OAAO,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,yBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC5E,CAAC;IACD,SAAS,CAAC,MAAe;QACvB,OAAO,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,yBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC5E,CAAC;IACD,MAAM;QACJ,IAAI,OAAO,GAAG,qBAAqB,CAAC,GAAG,EAAE,CAAC;QAC1C,IAAI,OAAO,KAAK,SAAS;YAAE,OAAO,GAAG,UAAU,CAAC,cAAc,EAAE,IAAI,OAAO,CAAC;QAC5E,mBAAW,CAAC,GAAG,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAC1D,CAAC;IACD,CAAC,uBAAc,CAAC,EAAE,CAAC,UAA6B,EAAE,EAAE;QAClD,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACrC,wBAAwB,CAAC,UAAU,EAAE,uBAAQ,CAAC,CAAC;IACjD,CAAC;CACF,CAAC;AAOF,SAAgB,qBAAqB,CACnC,KAAgC,EAChC,EAAE,IAAI,KAA+B,EAAE;IAEvC,MAAM,KAAK,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ,EAAE,CAAC,CAAC;IAClE,MAAM,IAAI,GAAG,IAAA,uBAAU,EAAC,KAAK,EAAE,IAAI,EAAE;QACnC,IAAI,EAAE,GAAG,IAAI,OAAO;QACpB,QAAQ,EAAE,KAAK;KAChB,CAAC,CAAC;IAEH,OAAO;QACL,IAAI;QACJ,GAAG,CAAC,MAAe;YACjB,OAAO,mBAAW,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,OAAO;gBACxC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;gBACnB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;QACD,GAAG,CAAC,KAAwD;YAC1D,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,EAAE,CAAC;gBACvC,IAAI,MAAM,IAAI,KAAK;oBAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC1C,IAAI,OAAO,IAAI,KAAK;oBAAE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBACjB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC;AAKD,IAAI,UAAU,GAAG,yBAAU,CAAC;AAC5B,IAAI,kBAAuD,CAAC;AAC5D,IAAI,gBAAqD,CAAC;AAE1D,SAAS,wBAAwB,CAC/B,WAA8B,EAC9B,QAAyB;IAEzB,UAAU,GAAG,WAAW,CAAC;IACzB,kBAAkB,EAAE,MAAM,EAAE,CAAC;IAC7B,gBAAgB,EAAE,MAAM,EAAE,CAAC;IAE3B,kBAAkB,GAAG,UAAU,CAAC,iBAAiB,CAAC,CAAC,KAAK,EAAE,EAAE;QAC1D,IAAI,uBAAQ,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;YACvC,yBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,IAAI,OAAO,CAAC,CAAC;QACtD,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,EAAE;QAC9D,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtB,MAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,IAAI,OAAO,CAAC;YAC3D,yBAAiB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AACD,wBAAwB,CAAC,UAAU,EAAE,uBAAQ,CAAC,CAAC;AAKlC,QAAA,qBAAqB,GAAG,MAAM,CAAC,MAAM,CAChD,IAAA,uBAAU,EAAU,KAAK,EAAE,EAAE,IAAI,EAAE,uBAAuB,EAAE,CAAC,EAC7D,EAAE,CAAC,uBAAc,CAAC,EAAE,GAAG,EAAE,CAAC,6BAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAC7D,CAAC;AAEF,gCAAiB,CAAC,qBAAqB,EAAE,EAAE,IAAI,CAAC,6BAAqB,CAAC,GAAG,CAAC,CAAC;AAC3E,gCAAiB,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAC,KAAK,EAAE,EAAE;IAClE,6BAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC"}