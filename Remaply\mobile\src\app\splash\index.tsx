import React from 'react';
import { View, StatusBar, Animated, StyleSheet } from 'react-native';
import { SplashScreenProps } from './types';
import { useSplashScreen } from './useSplashScreen';
import NeuralLogo from '../welcome/logo';

const SplashScreen: React.FC<SplashScreenProps> = ({ navigation }) => {
  const { logoScale, logoOpacity } = useSplashScreen();

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor="#FFFFFF"
        translucent={false}
      />

      <Animated.View
        style={[
          styles.logoContainer,
          {
            opacity: logoOpacity,
            transform: [{ scale: logoScale }],
          }
        ]}
      >
        <NeuralLogo size={160} interactive={false} />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF", // Pure white background
    justifyContent: "center",
    alignItems: "center",
  },
  logoContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
});

export default SplashScreen;