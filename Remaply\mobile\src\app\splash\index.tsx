import React from 'react';
import {
  View,
  StatusBar,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Text } from '@remaply/frontend-shared';
import { SplashScreenProps } from './types';
import { useSplashScreen } from './useSplashScreen';

const SplashScreen: React.FC<SplashScreenProps> = ({ navigation }) => {
  const { logoScale, logoOpacity, loadingOpacity } = useSplashScreen();

  return (
    <View className="flex-1">
      <StatusBar
        barStyle="light-content"
        backgroundColor="transparent"
        translucent
      />

      <LinearGradient
        colors={['#6C63FF', '#5A52E0', '#4A90E2']}
        className="flex-1"
      >
        <View className="flex-1 justify-center items-center">
          {/* Animated Logo */}
          <Animated.View
            className="mb-10"
            style={{
              opacity: logoOpacity,
              transform: [{ scale: logoScale }],
            }}
          >
            <View style={{ width: 120, height: 120, borderRadius: 60, backgroundColor: 'rgba(255, 255, 255, 0.15)', justifyContent: 'center', alignItems: 'center', borderWidth: 2, borderColor: 'rgba(255, 255, 255, 0.3)', shadowColor: '#000', shadowOffset: { width: 0, height: 8 }, shadowOpacity: 0.15, shadowRadius: 16, elevation: 8 }}>
                              <Text
                  variant="h1"
                  style={{ fontSize: 48, fontWeight: 'bold', color: '#FFFFFF' }}
                  children="R"
                />
            </View>
          </Animated.View>

          {/* Loading Text */}
          <Animated.View
            className="items-center"
            style={{ opacity: loadingOpacity }}
          >
            <Text
              variant="body2"
              style={{ fontSize: 14, color: 'rgba(255, 255, 255, 0.7)', textAlign: 'center' }}
              children="Loading Remaply..."
            />
          </Animated.View>
        </View>
      </LinearGradient>
    </View>
  );
};

export default SplashScreen; 