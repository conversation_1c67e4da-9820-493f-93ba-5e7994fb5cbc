{"version": 3, "file": "api.js", "sourceRoot": "", "sources": ["../../../src/runtime/web/api.ts"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;AAwFb,oBAaC;AAED,wCAEC;AAvGD,iCAA6D;AAE7D,yCAA8C;AAE9C,sCAA+C;AAC/C,iEAA2D;AAE3D,2CAA0C;AAAjC,wGAAA,UAAU,OAAA;AACnB,+CAA6C;AAApC,2GAAA,WAAW,OAAA;AACpB,6BAA4B;AAAnB,0FAAA,GAAG,OAAA;AAEZ,MAAM,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;AACzD,mDAAkD;AAAzC,gHAAA,cAAc,OAAA;AAChB,MAAM,UAAU,GAAe,CAAC,aAAa,EAAE,OAAO,EAAO,EAAE;IACpE,MAAM,OAAO,GAAG,IAAA,2BAAkB,EAAC,OAAO,CAAC,CAAC;IAQ5C,MAAM,gBAAgB,GAAG,IAAA,kBAAU,EAAC,SAAS,mBAAmB,CAC9D,EAAE,GAAG,KAAK,EAAuB,EACjC,GAAQ;QAER,IAAI,KAAK,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;YAC/B,OAAO,IAAA,qBAAa,EAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAC7C,CAAC;QAED,KAAK,GAAG,EAAE,GAAG,KAAK,EAAE,GAAG,EAAE,CAAC;QAC1B,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAGpC,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,EAAE,CAAC;gBACzC,IAAA,uBAAc,EACZ,KAAK,EACL;oBACE,KAAK,EAAE,IAAI;oBACX,CAAC,MAAM,CAAC,EAAE,MAAM;iBACjB,EACD,MAAM,EACN;oBACE,gBAAgB,EAAE,SAAS;iBAC5B,CACF,CAAC;YACJ,CAAC;YAED,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC9B,CAAC;QAED,IACE,UAAU,IAAI,aAAa;YAC3B,OAAO,aAAa,KAAK,UAAU;YACnC,aAAa,CAAC,QAAQ,KAAK,gBAAgB,EAC3C,CAAC;YACD,OAAO,KAAK,CAAC,UAAU,CAAC;YACxB,OAAQ,aAAqB,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;QACzD,CAAC;aAAM,IACL,OAAO,aAAa,KAAK,UAAU;YACnC,CAAC,CAAC,aAAa,CAAC,SAAS,YAAY,iBAAS,CAAC,EAC/C,CAAC;YACD,OAAO,KAAK,CAAC,UAAU,CAAC;YACxB,OAAQ,aAAqB,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC;aAAM,CAAC;YACN,OAAO,IAAA,qBAAa,EAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC,CAAC,CAAC;IACH,gBAAgB,CAAC,WAAW,GAAG,cAC7B,aAAa,CAAC,WAAW,IAAI,aAAa,CAAC,IAAI,IAAI,SACrD,EAAE,CAAC;IACH,wCAAiB,CAAC,GAAG,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;IACvD,OAAO,gBAAgB,CAAC;AAC1B,CAAC,CAAC;AA7DW,QAAA,UAAU,cA6DrB;AAGW,QAAA,UAAU,GAAG,kBAAU,CAAC;AAE9B,MAAM,yBAAyB,GAAG,CAAC,IAAY,EAAE,EAAE;IACxD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;IACpE,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AALW,QAAA,yBAAyB,6BAKpC;AAEF,SAAgB,IAAI,CAClB,SAAY;IAEZ,MAAM,UAAU,GAA2B,EAAE,CAAC;IAE9C,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;QACrD,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,UAAU,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,UAAU,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC5C,CAAC;IACH,CAAC;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAgB,cAAc;IAC5B,OAAO,SAAS,CAAC;AACnB,CAAC"}