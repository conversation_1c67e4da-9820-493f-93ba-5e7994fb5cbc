import { createElement as originalCreateElement } from "react";
export { Fragment } from "react";
export declare const jsxs: import("../types").JSXFunction;
export declare const jsx: import("../types").JSXFunction;
export declare const jsxDEV: import("../types").JSXFunction;
export declare const createInteropElement: import("../types").JSXFunction;
export declare const createElement: typeof originalCreateElement;
