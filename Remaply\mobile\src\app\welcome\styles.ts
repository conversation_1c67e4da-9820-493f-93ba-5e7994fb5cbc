import { StyleSheet } from 'react-native';

export const welcomeStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF9F6', // Soft Cream background
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingTop: 60,
  },
  logoContainer: {
    marginBottom: 48,
  },
  logoWrapper: {
    width: 80,
    height: 80,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoShape1: {
    position: 'absolute',
    width: 32,
    height: 32,
    backgroundColor: '#FF7CA3', // Blush Pink
    borderRadius: 8,
    top: 8,
    left: 8,
    transform: [{ rotate: '45deg' }],
  },
  logoShape2: {
    position: 'absolute',
    width: 24,
    height: 24,
    backgroundColor: '#FFA987', // Coral Peach
    borderRadius: 6,
    top: 16,
    right: 8,
    transform: [{ rotate: '15deg' }],
  },
  logoShape3: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: '#FF3B30', // Warm Red
    borderRadius: 4,
    bottom: 12,
    left: 16,
    transform: [{ rotate: '-30deg' }],
  },
  titleContainer: {
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#2C2C2C', // Charcoal Gray
    textAlign: 'center',
    lineHeight: 36,
  },
  subtitleContainer: {
    marginBottom: 48,
    paddingHorizontal: 24,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '400',
    color: '#6E6E6E', // Muted Gray
    textAlign: 'center',
    lineHeight: 24,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#EAEAEA', // Mist Gray
    marginHorizontal: 4,
  },
  progressDotActive: {
    backgroundColor: '#2C2C2C', // Charcoal Gray
  },
  bottomContainer: {
    paddingHorizontal: 32,
    paddingBottom: 48,
  },
  getStartedButton: {
    backgroundColor: '#2C2C2C', // Charcoal Gray
    borderRadius: 12,
    paddingVertical: 16,
  },
});
