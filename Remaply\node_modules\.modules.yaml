hoistPattern:
  - '*'
hoistedDependencies:
  '@0no-co/graphql.web@1.1.2':
    '@0no-co/graphql.web': private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.5(@babel/core@7.28.0)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.27.1':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/highlight@7.25.9':
    '@babel/highlight': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-proposal-decorators@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-proposal-decorators': private
  '@babel/plugin-proposal-export-default-from@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-proposal-export-default-from': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.28.0)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-decorators': private
  '@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-dynamic-import': private
  '@babel/plugin-syntax-export-default-from@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-export-default-from': private
  '@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-flow': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoping@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-classes@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-flow-strip-types@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-flow-strip-types': private
  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.27.7(@babel/core@7.28.0)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-react-display-name@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-display-name': private
  '@babel/plugin-transform-react-jsx-development@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-development': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx': private
  '@babel/plugin-transform-react-pure-annotations@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-pure-annotations': private
  '@babel/plugin-transform-regenerator@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-runtime@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-runtime': private
  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typescript@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-typescript': private
  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/preset-react@7.27.1(@babel/core@7.28.0)':
    '@babel/preset-react': private
  '@babel/preset-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/preset-typescript': private
  '@babel/runtime-corejs3@7.28.0':
    '@babel/runtime-corejs3': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
    '@babel/traverse--for-generate-function-map': private
  '@babel/types@7.28.0':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@better-auth/expo@1.2.9(better-auth@1.2.9)':
    '@better-auth/expo': private
  '@better-auth/utils@0.2.5':
    '@better-auth/utils': private
  '@better-fetch/fetch@1.1.18':
    '@better-fetch/fetch': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@drizzle-team/brocli@0.10.2':
    '@drizzle-team/brocli': private
  '@egjs/hammerjs@2.0.17':
    '@egjs/hammerjs': private
  '@electric-sql/pglite@0.2.17':
    '@electric-sql/pglite': private
  '@esbuild-kit/core-utils@3.3.2':
    '@esbuild-kit/core-utils': private
  '@esbuild-kit/esm-loader@2.6.5':
    '@esbuild-kit/esm-loader': private
  '@esbuild/aix-ppc64@0.25.6':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.6':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.6':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.6':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.6':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.6':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.6':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.6':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.6':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.6':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.6':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.6':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.6':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.6':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.6':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.6':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.6':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.6':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.6':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.6':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.6':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.6':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.6':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.6':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.6':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.6':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.31.0(jiti@1.21.7))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/compat@1.3.1(eslint@9.31.0(jiti@1.21.7))':
    '@eslint/compat': private
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.0':
    '@eslint/config-helpers': private
  '@eslint/core@0.15.1':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.31.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.3':
    '@eslint/plugin-kit': private
  '@expo/cli@0.24.20(bufferutil@4.0.9)':
    '@expo/cli': private
  '@expo/code-signing-certificates@0.0.5':
    '@expo/code-signing-certificates': private
  '@expo/config-plugins@10.1.2':
    '@expo/config-plugins': private
  '@expo/config-types@53.0.5':
    '@expo/config-types': private
  '@expo/config@11.0.13':
    '@expo/config': private
  '@expo/devcert@1.2.0':
    '@expo/devcert': private
  '@expo/env@1.0.7':
    '@expo/env': private
  '@expo/fingerprint@0.13.4':
    '@expo/fingerprint': private
  '@expo/image-utils@0.7.6':
    '@expo/image-utils': private
  '@expo/json-file@9.1.5':
    '@expo/json-file': private
  '@expo/metro-config@0.20.17':
    '@expo/metro-config': private
  '@expo/osascript@2.2.5':
    '@expo/osascript': private
  '@expo/package-manager@1.8.6':
    '@expo/package-manager': private
  '@expo/plist@0.3.5':
    '@expo/plist': private
  '@expo/prebuild-config@9.0.11':
    '@expo/prebuild-config': private
  '@expo/sdk-runtime-versions@1.0.0':
    '@expo/sdk-runtime-versions': private
  '@expo/spawn-async@1.7.2':
    '@expo/spawn-async': private
  '@expo/sudo-prompt@9.3.2':
    '@expo/sudo-prompt': private
  '@expo/vector-icons@14.1.0(expo-font@13.3.2(expo@53.0.19(@babel/core@7.28.0)(bufferutil@4.0.9)(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0)':
    '@expo/vector-icons': private
  '@expo/ws-tunnel@1.0.6':
    '@expo/ws-tunnel': private
  '@expo/xcpretty@4.3.2':
    '@expo/xcpretty': private
  '@floating-ui/core@1.7.2':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.2':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.4(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@hapi/hoek@9.3.0':
    '@hapi/hoek': private
  '@hapi/topo@5.1.0':
    '@hapi/topo': private
  '@hexagon/base64@1.1.28':
    '@hexagon/base64': private
  '@hookform/resolvers@5.1.1(react-hook-form@7.60.0(react@19.0.0))':
    '@hookform/resolvers': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@ianvs/prettier-plugin-sort-imports@4.5.1(prettier@3.6.2)':
    '@ianvs/prettier-plugin-sort-imports': private
  '@isaacs/balanced-match@4.0.1':
    '@isaacs/balanced-match': private
  '@isaacs/brace-expansion@5.0.0':
    '@isaacs/brace-expansion': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@isaacs/ttlcache@1.4.1':
    '@isaacs/ttlcache': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@29.7.0':
    '@jest/console': private
  '@jest/core@29.7.0(ts-node@10.9.2(@types/node@22.16.3)(typescript@5.8.3))':
    '@jest/core': private
  '@jest/create-cache-key-function@29.7.0':
    '@jest/create-cache-key-function': private
  '@jest/environment@29.7.0':
    '@jest/environment': private
  '@jest/expect-utils@29.7.0':
    '@jest/expect-utils': private
  '@jest/expect@29.7.0':
    '@jest/expect': private
  '@jest/fake-timers@29.7.0':
    '@jest/fake-timers': private
  '@jest/globals@29.7.0':
    '@jest/globals': private
  '@jest/reporters@29.7.0':
    '@jest/reporters': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jest/source-map@29.6.3':
    '@jest/source-map': private
  '@jest/test-result@29.7.0':
    '@jest/test-result': private
  '@jest/test-sequencer@29.7.0':
    '@jest/test-sequencer': private
  '@jest/transform@29.7.0':
    '@jest/transform': private
  '@jest/types@29.6.3':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.10':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@levischuck/tiny-cbor@0.2.11':
    '@levischuck/tiny-cbor': private
  '@neondatabase/serverless@0.9.5':
    '@neondatabase/serverless': private
  '@next/env@14.0.4':
    '@next/env': private
  '@next/eslint-plugin-next@15.3.5':
    '@next/eslint-plugin-next': private
  '@next/swc-darwin-arm64@14.0.4':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@14.0.4':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@14.0.4':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@14.0.4':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@14.0.4':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@14.0.4':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@14.0.4':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-ia32-msvc@14.0.4':
    '@next/swc-win32-ia32-msvc': private
  '@next/swc-win32-x64-msvc@14.0.4':
    '@next/swc-win32-x64-msvc': private
  '@noble/ciphers@0.6.0':
    '@noble/ciphers': private
  '@noble/hashes@1.8.0':
    '@noble/hashes': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@peculiar/asn1-android@2.3.16':
    '@peculiar/asn1-android': private
  '@peculiar/asn1-ecc@2.3.15':
    '@peculiar/asn1-ecc': private
  '@peculiar/asn1-rsa@2.3.15':
    '@peculiar/asn1-rsa': private
  '@peculiar/asn1-schema@2.3.15':
    '@peculiar/asn1-schema': private
  '@peculiar/asn1-x509@2.3.15':
    '@peculiar/asn1-x509': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-accessible-icon@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-accessible-icon': private
  '@radix-ui/react-accordion@1.2.11(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-accordion': private
  '@radix-ui/react-alert-dialog@1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-alert-dialog': private
  '@radix-ui/react-arrow@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-aspect-ratio@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-aspect-ratio': private
  '@radix-ui/react-avatar@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-avatar': private
  '@radix-ui/react-checkbox@1.3.2(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-checkbox': private
  '@radix-ui/react-collapsible@1.1.11(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-collapsible': private
  '@radix-ui/react-collection@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context-menu@2.2.15(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-context-menu': private
  '@radix-ui/react-context@1.1.2(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-dialog@1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-dialog': private
  '@radix-ui/react-direction@1.1.1(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-dropdown-menu@2.1.15(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-dropdown-menu': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-form@0.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-form': private
  '@radix-ui/react-hover-card@1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-hover-card': private
  '@radix-ui/react-icons@1.3.2(react@19.0.0)':
    '@radix-ui/react-icons': private
  '@radix-ui/react-id@1.1.1(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-label@2.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-label': private
  '@radix-ui/react-menu@2.1.15(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-menubar@1.1.15(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-menubar': private
  '@radix-ui/react-navigation-menu@1.2.13(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-navigation-menu': private
  '@radix-ui/react-one-time-password-field@0.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-one-time-password-field': private
  '@radix-ui/react-password-toggle-field@0.1.2(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-password-toggle-field': private
  '@radix-ui/react-popover@1.1.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-popover': private
  '@radix-ui/react-popper@1.2.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-progress@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-progress': private
  '@radix-ui/react-radio-group@1.3.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-radio-group': private
  '@radix-ui/react-roving-focus@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-scroll-area@1.2.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-scroll-area': private
  '@radix-ui/react-select@2.2.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-select': private
  '@radix-ui/react-separator@1.1.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-separator': private
  '@radix-ui/react-slider@1.3.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-slider': private
  '@radix-ui/react-slot@1.2.3(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-slot': private
  '@radix-ui/react-switch@1.2.5(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-switch': private
  '@radix-ui/react-tabs@1.1.12(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-tabs': private
  '@radix-ui/react-toast@1.2.14(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-toast': private
  '@radix-ui/react-toggle-group@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-toggle-group': private
  '@radix-ui/react-toggle@1.1.9(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-toggle': private
  '@radix-ui/react-toolbar@1.1.10(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-toolbar': private
  '@radix-ui/react-tooltip@1.2.7(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-tooltip': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-is-hydrated@0.1.0(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-use-is-hydrated': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.8)(react@19.0.0)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@react-native-async-storage/async-storage@1.23.1(react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))':
    '@react-native-async-storage/async-storage': private
  '@react-native/assets-registry@0.79.5':
    '@react-native/assets-registry': private
  '@react-native/babel-plugin-codegen@0.79.5(@babel/core@7.28.0)':
    '@react-native/babel-plugin-codegen': private
  '@react-native/babel-preset@0.79.5(@babel/core@7.28.0)':
    '@react-native/babel-preset': private
  '@react-native/codegen@0.79.5(@babel/core@7.28.0)':
    '@react-native/codegen': private
  '@react-native/community-cli-plugin@0.79.5(bufferutil@4.0.9)':
    '@react-native/community-cli-plugin': private
  '@react-native/debugger-frontend@0.79.5':
    '@react-native/debugger-frontend': private
  '@react-native/dev-middleware@0.79.5(bufferutil@4.0.9)':
    '@react-native/dev-middleware': private
  '@react-native/gradle-plugin@0.79.5':
    '@react-native/gradle-plugin': private
  '@react-native/js-polyfills@0.79.5':
    '@react-native/js-polyfills': private
  '@react-native/normalize-colors@0.74.89':
    '@react-native/normalize-colors': private
  '@react-native/virtualized-lists@0.79.5(@types/react@19.1.8)(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0)':
    '@react-native/virtualized-lists': private
  '@react-navigation/core@6.4.17(react@19.0.0)':
    '@react-navigation/core': private
  '@react-navigation/elements@1.3.31(@react-navigation/native@6.1.18(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.4.0(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0)':
    '@react-navigation/elements': private
  '@react-navigation/native-stack@6.11.0(@react-navigation/native@6.1.18(react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.5.2(react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native-screens@4.11.1(react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0)':
    '@react-navigation/native-stack': private
  '@react-navigation/native@6.1.18(react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0)':
    '@react-navigation/native': private
  '@react-navigation/routers@6.1.9':
    '@react-navigation/routers': private
  ? '@remaply/frontend-shared@file:frontend-shared(@react-navigation/native-stack@6.11.0(@react-navigation/native@6.1.18(react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.5.2(react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native-screens@4.11.1(react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(@react-navigation/native@6.1.18(react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(expo@53.0.19(@babel/core@7.28.0)(bufferutil@4.0.9)(react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0)'
  : '@remaply/frontend-shared': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.12.0':
    '@rushstack/eslint-patch': private
  '@sideway/address@4.1.5':
    '@sideway/address': private
  '@sideway/formula@3.0.1':
    '@sideway/formula': private
  '@sideway/pinpoint@2.0.0':
    '@sideway/pinpoint': private
  '@simplewebauthn/browser@13.1.2':
    '@simplewebauthn/browser': private
  '@simplewebauthn/server@13.1.2':
    '@simplewebauthn/server': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@10.3.0':
    '@sinonjs/fake-timers': private
  '@standard-schema/utils@0.3.0':
    '@standard-schema/utils': private
  '@swc/helpers@0.5.2':
    '@swc/helpers': private
  '@t3-oss/env-core@0.13.8(typescript@5.8.3)(zod@3.25.76)':
    '@t3-oss/env-core': private
  '@t3-oss/env-nextjs@0.13.8(typescript@5.8.3)(zod@3.25.76)':
    '@t3-oss/env-nextjs': private
  '@tootallnate/quickjs-emscripten@0.23.0':
    '@tootallnate/quickjs-emscripten': private
  '@trpc/server@11.4.3(typescript@5.8.3)':
    '@trpc/server': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@turbo/workspaces@2.5.4':
    '@turbo/workspaces': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/bcryptjs@3.0.0':
    '@types/bcryptjs': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/glob@7.2.0':
    '@types/glob': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/hammerjs@2.0.46':
    '@types/hammerjs': private
  '@types/hoist-non-react-statics@3.3.6':
    '@types/hoist-non-react-statics': private
  '@types/inquirer@6.5.0':
    '@types/inquirer': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/jest@29.5.14':
    '@types/jest': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/minimatch@6.0.0':
    '@types/minimatch': private
  '@types/node@22.16.3':
    '@types/node': private
  '@types/pg@8.11.6':
    '@types/pg': private
  '@types/react-native@0.73.0(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0)':
    '@types/react-native': private
  '@types/sanitize-html@2.16.0':
    '@types/sanitize-html': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/through@0.0.33':
    '@types/through': private
  '@types/tinycolor2@1.4.6':
    '@types/tinycolor2': private
  '@types/uuid@10.0.0':
    '@types/uuid': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@typescript-eslint/eslint-plugin@8.36.0(@typescript-eslint/parser@8.36.0(eslint@9.31.0(jiti@1.21.7))(typescript@5.8.3))(eslint@9.31.0(jiti@1.21.7))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.36.0(eslint@9.31.0(jiti@1.21.7))(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.36.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.36.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.36.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.36.0(eslint@9.31.0(jiti@1.21.7))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.36.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.36.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.36.0(eslint@9.31.0(jiti@1.21.7))(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.36.0':
    '@typescript-eslint/visitor-keys': private
  '@unrs/resolver-binding-android-arm-eabi@1.11.1':
    '@unrs/resolver-binding-android-arm-eabi': private
  '@unrs/resolver-binding-android-arm64@1.11.1':
    '@unrs/resolver-binding-android-arm64': private
  '@unrs/resolver-binding-darwin-arm64@1.11.1':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.11.1':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.11.1':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.11.1':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-musl@1.11.1':
    '@unrs/resolver-binding-linux-riscv64-musl': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.11.1':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.11.1':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.11.1':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.11.1':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.11.1':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.11.1':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.11.1':
    '@unrs/resolver-binding-win32-x64-msvc': private
  '@urql/core@5.2.0':
    '@urql/core': private
  '@urql/exchange-retry@1.3.2(@urql/core@5.2.0)':
    '@urql/exchange-retry': private
  '@vercel/postgres@0.10.0':
    '@vercel/postgres': private
  '@xmldom/xmldom@0.8.10':
    '@xmldom/xmldom': private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@1.3.8:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  agent-base@7.1.4:
    agent-base: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv@6.12.6:
    ajv: private
  anser@1.4.10:
    anser: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.6:
    aria-hidden: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.9:
    array-includes: private
  array-timsort@1.0.3:
    array-timsort: private
  array-union@2.1.0:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  asap@2.0.6:
    asap: private
  asn1js@3.0.6:
    asn1js: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  ast-types@0.13.4:
    ast-types: private
  async-function@1.0.0:
    async-function: private
  async-limiter@1.0.1:
    async-limiter: private
  async@3.2.6:
    async: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  babel-jest@29.7.0(@babel/core@7.28.0):
    babel-jest: private
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@29.6.3:
    babel-plugin-jest-hoist: private
  babel-plugin-polyfill-corejs2@0.4.14(@babel/core@7.28.0):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.13.0(@babel/core@7.28.0):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.5(@babel/core@7.28.0):
    babel-plugin-polyfill-regenerator: private
  babel-plugin-react-native-web@0.19.13:
    babel-plugin-react-native-web: private
  babel-plugin-syntax-hermes-parser@0.25.1:
    babel-plugin-syntax-hermes-parser: private
  babel-plugin-transform-flow-enums@0.0.2(@babel/core@7.28.0):
    babel-plugin-transform-flow-enums: private
  babel-preset-current-node-syntax@1.1.0(@babel/core@7.28.0):
    babel-preset-current-node-syntax: private
  babel-preset-expo@13.2.3(@babel/core@7.28.0):
    babel-preset-expo: private
  babel-preset-jest@29.6.3(@babel/core@7.28.0):
    babel-preset-jest: private
  backend:
    '@remaply/backend': private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  basic-ftp@5.0.5:
    basic-ftp: private
  bcryptjs@3.0.2:
    bcryptjs: private
  better-auth@1.2.9:
    better-auth: private
  better-call@1.0.12:
    better-call: private
  better-opn@3.0.2:
    better-opn: private
  big-integer@1.6.52:
    big-integer: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bl@4.1.0:
    bl: private
  bplist-creator@0.1.0:
    bplist-creator: private
  bplist-parser@0.3.2:
    bplist-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  bs-logger@0.2.6:
    bs-logger: private
  bser@2.1.1:
    bser: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@5.7.1:
    buffer: private
  bufferutil@4.0.9:
    bufferutil: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  caller-callsite@2.0.0:
    caller-callsite: private
  caller-path@2.0.0:
    caller-path: private
  callsites@3.1.0:
    callsites: private
  camel-case@3.0.0:
    camel-case: private
  camelcase-css@2.0.1:
    camelcase-css: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  change-case@3.1.0:
    change-case: private
  char-regex@1.0.2:
    char-regex: private
  chardet@0.7.0:
    chardet: private
  chokidar@3.6.0:
    chokidar: private
  chownr@3.0.0:
    chownr: private
  chrome-launcher@0.15.2:
    chrome-launcher: private
  chromium-edge-launcher@0.2.0:
    chromium-edge-launcher: private
  ci-info@3.9.0:
    ci-info: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  class-variance-authority@0.7.1:
    class-variance-authority: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-width@3.0.0:
    cli-width: private
  client-only@0.0.1:
    client-only: private
  cliui@8.0.1:
    cliui: private
  clone@1.0.4:
    clone: private
  clsx@2.1.1:
    clsx: private
  co@4.6.0:
    co: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  commander@10.0.1:
    commander: private
  comment-json@4.2.5:
    comment-json: private
  compressible@2.0.18:
    compressible: private
  compression@1.8.0:
    compression: private
  concat-map@0.0.1:
    concat-map: private
  connect@3.7.0:
    connect: private
  constant-case@2.0.0:
    constant-case: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-anything@3.0.5:
    copy-anything: private
  core-js-compat@3.44.0:
    core-js-compat: private
  core-js-pure@3.44.0:
    core-js-pure: private
  core-util-is@1.0.3:
    core-util-is: private
  cosmiconfig@5.2.1:
    cosmiconfig: private
  create-jest@29.7.0(@types/node@22.16.3)(ts-node@10.9.2(@types/node@22.16.3)(typescript@5.8.3)):
    create-jest: private
  create-require@1.1.1:
    create-require: private
  cross-fetch@3.2.0:
    cross-fetch: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crypto-random-string@2.0.0:
    crypto-random-string: private
  css-in-js-utils@3.1.0:
    css-in-js-utils: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-uri-to-buffer@6.0.2:
    data-uri-to-buffer: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  date-fns@2.30.0:
    date-fns: private
  dayjs@1.11.13:
    dayjs: private
  debug@4.4.1:
    debug: private
  decode-uri-component@0.2.2:
    decode-uri-component: private
  dedent@1.6.0:
    dedent: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@2.2.1:
    deepmerge: private
  defaults@1.0.4:
    defaults: private
  define-data-property@1.1.4:
    define-data-property: private
  define-lazy-prop@2.0.0:
    define-lazy-prop: private
  define-properties@1.2.1:
    define-properties: private
  defu@6.1.4:
    defu: private
  degenerator@5.0.1:
    degenerator: private
  del@5.1.0:
    del: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  detect-libc@1.0.3:
    detect-libc: private
  detect-newline@3.1.0:
    detect-newline: private
  detect-node-es@1.1.0:
    detect-node-es: private
  didyoumean@1.2.2:
    didyoumean: private
  diff-sequences@29.6.3:
    diff-sequences: private
  diff@4.0.2:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  dlv@1.1.3:
    dlv: private
  doctrine@2.1.0:
    doctrine: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@5.0.3:
    domhandler: private
  domutils@3.2.2:
    domutils: private
  dot-case@2.1.1:
    dot-case: private
  dotenv-cli@8.0.0:
    dotenv-cli: private
  dotenv-expand@10.0.0:
    dotenv-expand: private
  dotenv@16.6.1:
    dotenv: private
  drizzle-kit@0.31.4:
    drizzle-kit: private
  drizzle-orm@0.44.2(@electric-sql/pglite@0.2.17)(@opentelemetry/api@1.9.0)(@types/pg@8.11.6)(@vercel/postgres@0.10.0)(kysely@0.28.2)(pg@8.16.3):
    drizzle-orm: private
  drizzle-zod@0.8.2(drizzle-orm@0.44.2(@electric-sql/pglite@0.2.17)(@opentelemetry/api@1.9.0)(@types/pg@8.11.6)(@vercel/postgres@0.10.0)(kysely@0.28.2)(pg@8.16.3))(zod@3.25.76):
    drizzle-zod: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.182:
    electron-to-chromium: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@9.2.2:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  entities@4.5.0:
    entities: private
  env-editor@0.4.2:
    env-editor: private
  error-ex@1.3.2:
    error-ex: private
  error-stack-parser@2.1.4:
    error-stack-parser: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild-register@3.6.0(esbuild@0.25.6):
    esbuild-register: private
  esbuild@0.25.6:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-config-next@14.0.4(eslint@9.31.0(jiti@1.21.7))(typescript@5.8.3):
    eslint-config-next: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0(eslint@9.31.0(jiti@1.21.7)))(eslint@9.31.0(jiti@1.21.7)):
    eslint-import-resolver-typescript: private
  eslint-module-utils@2.12.1(@typescript-eslint/parser@8.36.0(eslint@9.31.0(jiti@1.21.7))(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint@9.31.0(jiti@1.21.7)):
    eslint-module-utils: private
  eslint-plugin-import@2.32.0(@typescript-eslint/parser@8.36.0(eslint@9.31.0(jiti@1.21.7))(typescript@5.8.3))(eslint@9.31.0(jiti@1.21.7)):
    eslint-plugin-import: private
  eslint-plugin-jsx-a11y@6.10.2(eslint@9.31.0(jiti@1.21.7)):
    eslint-plugin-jsx-a11y: private
  eslint-plugin-react-hooks@6.0.0(eslint@9.31.0(jiti@1.21.7)):
    eslint-plugin-react-hooks: private
  eslint-plugin-react@7.37.5(eslint@9.31.0(jiti@1.21.7)):
    eslint-plugin-react: private
  eslint-plugin-turbo@2.5.4(eslint@9.31.0(jiti@1.21.7))(turbo@2.5.4):
    eslint-plugin-turbo: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  eslint@9.31.0(jiti@1.21.7):
    eslint: private
  espree@10.4.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-target-shim@5.0.1:
    event-target-shim: private
  exec-async@2.2.0:
    exec-async: private
  execa@5.1.1:
    execa: private
  exit@0.1.2:
    exit: private
  expect@29.7.0:
    expect: private
  expo-asset@11.1.7(expo@53.0.19(@babel/core@7.28.0)(bufferutil@4.0.9)(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0):
    expo-asset: private
  expo-constants@17.1.7(expo@53.0.19(@babel/core@7.28.0)(bufferutil@4.0.9)(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0)):
    expo-constants: private
  expo-file-system@18.1.11(expo@53.0.19(@babel/core@7.28.0)(bufferutil@4.0.9)(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0)):
    expo-file-system: private
  expo-font@13.3.2(expo@53.0.19(@babel/core@7.28.0)(bufferutil@4.0.9)(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react@19.0.0):
    expo-font: private
  expo-keep-awake@14.1.4(expo@53.0.19(@babel/core@7.28.0)(bufferutil@4.0.9)(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react@19.0.0):
    expo-keep-awake: private
  expo-linear-gradient@13.0.2(expo@53.0.19(@babel/core@7.28.0)(bufferutil@4.0.9)(react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0)):
    expo-linear-gradient: private
  expo-modules-autolinking@2.1.14:
    expo-modules-autolinking: private
  expo-modules-core@2.4.2:
    expo-modules-core: private
  expo-status-bar@2.2.3(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0):
    expo-status-bar: private
  expo@53.0.19(@babel/core@7.28.0)(bufferutil@4.0.9)(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0):
    expo: private
  exponential-backoff@3.1.2:
    exponential-backoff: private
  external-editor@3.1.0:
    external-editor: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.1:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fbjs-css-vars@1.0.2:
    fbjs-css-vars: private
  fbjs@3.0.5:
    fbjs: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  figures@3.2.0:
    figures: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  filter-obj@1.1.0:
    filter-obj: private
  finalhandler@1.1.2:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  flow-enums-runtime@0.0.6:
    flow-enums-runtime: private
  fontfaceobserver@2.3.0:
    fontfaceobserver: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  formik@2.4.6(react@19.0.0):
    formik: private
  freeport-async@2.0.0:
    freeport-async: private
  fresh@0.5.2:
    fresh: private
  fs-extra@10.1.0:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  get-uri@6.0.5:
    get-uri: private
  getenv@2.0.0:
    getenv: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@7.2.3:
    glob: private
  globals@14.0.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@10.0.2:
    globby: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  gradient-string@2.0.2:
    gradient-string: private
  graphemer@1.4.0:
    graphemer: private
  handlebars@4.7.8:
    handlebars: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-own-prop@2.0.0:
    has-own-prop: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  header-case@1.0.1:
    header-case: private
  hermes-estree@0.25.1:
    hermes-estree: private
  hermes-parser@0.25.1:
    hermes-parser: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  hosted-git-info@7.0.2:
    hosted-git-info: private
  html-escaper@2.0.2:
    html-escaper: private
  htmlparser2@8.0.2:
    htmlparser2: private
  http-errors@2.0.0:
    http-errors: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  hyphenate-style-name@1.1.0:
    hyphenate-style-name: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@7.0.5:
    ignore: private
  image-size@1.2.1:
    image-size: private
  import-fresh@3.3.1:
    import-fresh: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  inline-style-prefixer@7.0.1:
    inline-style-prefixer: private
  inquirer@8.2.6:
    inquirer: private
  internal-slot@1.1.0:
    internal-slot: private
  invariant@2.2.4:
    invariant: private
  ip-address@9.0.5:
    ip-address: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-bun-module@2.0.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-directory@0.3.1:
    is-directory: private
  is-docker@2.2.1:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-interactive@1.0.0:
    is-interactive: private
  is-lower-case@1.1.3:
    is-lower-case: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-cwd@2.2.0:
    is-path-cwd: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@2.1.0:
    is-plain-obj: private
  is-plain-object@5.0.0:
    is-plain-object: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-upper-case@1.1.2:
    is-upper-case: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-what@4.1.16:
    is-what: private
  is-wsl@2.2.0:
    is-wsl: private
  isarray@2.0.5:
    isarray: private
  isbinaryfile@4.0.10:
    isbinaryfile: private
  isexe@2.0.0:
    isexe: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@6.0.3:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@4.1.1:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jest-changed-files@29.7.0:
    jest-changed-files: private
  jest-circus@29.7.0:
    jest-circus: private
  jest-cli@29.7.0(@types/node@22.16.3)(ts-node@10.9.2(@types/node@22.16.3)(typescript@5.8.3)):
    jest-cli: private
  jest-config@29.7.0(@types/node@22.16.3)(ts-node@10.9.2(@types/node@22.16.3)(typescript@5.8.3)):
    jest-config: private
  jest-diff@29.7.0:
    jest-diff: private
  jest-docblock@29.7.0:
    jest-docblock: private
  jest-each@29.7.0:
    jest-each: private
  jest-environment-node@29.7.0:
    jest-environment-node: private
  jest-get-type@29.6.3:
    jest-get-type: private
  jest-haste-map@29.7.0:
    jest-haste-map: private
  jest-leak-detector@29.7.0:
    jest-leak-detector: private
  jest-matcher-utils@29.7.0:
    jest-matcher-utils: private
  jest-message-util@29.7.0:
    jest-message-util: private
  jest-mock@29.7.0:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  jest-regex-util@29.6.3:
    jest-regex-util: private
  jest-resolve-dependencies@29.7.0:
    jest-resolve-dependencies: private
  jest-resolve@29.7.0:
    jest-resolve: private
  jest-runner@29.7.0:
    jest-runner: private
  jest-runtime@29.7.0:
    jest-runtime: private
  jest-snapshot@29.7.0:
    jest-snapshot: private
  jest-util@29.7.0:
    jest-util: private
  jest-validate@29.7.0:
    jest-validate: private
  jest-watcher@29.7.0:
    jest-watcher: private
  jest-worker@29.7.0:
    jest-worker: private
  jest@29.7.0(@types/node@22.16.3)(ts-node@10.9.2(@types/node@22.16.3)(typescript@5.8.3)):
    jest: private
  jimp-compact@0.16.1:
    jimp-compact: private
  jiti@1.21.7:
    jiti: private
  joi@17.13.3:
    joi: private
  jose@5.10.0:
    jose: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  jsc-safe-url@0.2.4:
    jsc-safe-url: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-better-errors@1.0.2:
    json-parse-better-errors: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keyv@4.5.4:
    keyv: private
  kleur@3.0.3:
    kleur: private
  kysely@0.28.2:
    kysely: private
  lan-network@0.1.7:
    lan-network: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lighthouse-logger@1.4.2:
    lighthouse-logger: private
  lightningcss-darwin-arm64@1.27.0:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.27.0:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.27.0:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.27.0:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.27.0:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.27.0:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.27.0:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.27.0:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.27.0:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.27.0:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.27.0:
    lightningcss: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.get@4.4.2:
    lodash.get: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.throttle@4.1.1:
    lodash.throttle: private
  lodash@4.17.21:
    lodash: private
  log-symbols@2.2.0:
    log-symbols: private
  loose-envify@1.4.0:
    loose-envify: private
  lower-case-first@1.0.2:
    lower-case-first: private
  lower-case@1.1.4:
    lower-case: private
  lru-cache@5.1.1:
    lru-cache: private
  make-dir@4.0.0:
    make-dir: private
  make-error@1.3.6:
    make-error: private
  makeerror@1.0.12:
    makeerror: private
  marky@1.3.0:
    marky: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  memoize-one@6.0.0:
    memoize-one: private
  merge-options@3.0.4:
    merge-options: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  metro-babel-transformer@0.82.5:
    metro-babel-transformer: private
  metro-cache-key@0.82.5:
    metro-cache-key: private
  metro-cache@0.82.5:
    metro-cache: private
  metro-config@0.82.5(bufferutil@4.0.9):
    metro-config: private
  metro-core@0.82.5:
    metro-core: private
  metro-file-map@0.82.5:
    metro-file-map: private
  metro-minify-terser@0.82.5:
    metro-minify-terser: private
  metro-resolver@0.82.5:
    metro-resolver: private
  metro-runtime@0.82.5:
    metro-runtime: private
  metro-source-map@0.82.5:
    metro-source-map: private
  metro-symbolicate@0.82.5:
    metro-symbolicate: private
  metro-transform-plugins@0.82.5:
    metro-transform-plugins: private
  metro-transform-worker@0.82.5(bufferutil@4.0.9):
    metro-transform-worker: private
  metro@0.82.5(bufferutil@4.0.9):
    metro: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@0.5.6:
    mkdirp: private
  mobile:
    '@remaply/mobile': private
  ms@2.1.3:
    ms: private
  mute-stream@0.0.8:
    mute-stream: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  nanostores@0.11.4:
    nanostores: private
  napi-postinstall@0.3.0:
    napi-postinstall: private
  nativewind@4.1.23(react-native-reanimated@3.17.5(@babel/core@7.28.0)(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.4.0(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0)(tailwindcss@3.4.17(ts-node@10.9.2(@types/node@22.16.3)(typescript@5.8.3))):
    nativewind: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.4:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  nested-error-stacks@2.0.1:
    nested-error-stacks: private
  netmask@2.0.2:
    netmask: private
  next-themes@0.4.6(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    next-themes: private
  next@14.0.4(@babel/core@7.28.0)(@opentelemetry/api@1.9.0)(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    next: private
  no-case@2.3.2:
    no-case: private
  node-fetch@2.7.0:
    node-fetch: private
  node-forge@1.3.1:
    node-forge: private
  node-gyp-build@4.8.4:
    node-gyp-build: private
  node-int64@0.4.0:
    node-int64: private
  node-plop@0.26.3:
    node-plop: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-package-arg@11.0.3:
    npm-package-arg: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nullthrows@1.1.1:
    nullthrows: private
  ob1@0.82.5:
    ob1: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  obuf@1.1.2:
    obuf: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.0.2:
    on-headers: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  open@7.4.2:
    open: private
  optionator@0.9.4:
    optionator: private
  ora@3.4.0:
    ora: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@3.0.0:
    p-map: private
  p-try@2.2.0:
    p-try: private
  pac-proxy-agent@7.2.0:
    pac-proxy-agent: private
  pac-resolver@7.0.1:
    pac-resolver: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  packages/api:
    '@remaply/api': private
  packages/auth:
    '@remaply/auth': private
  packages/db:
    '@remaply/db': private
  packages/ui:
    '@remaply/ui': private
  packages/validators:
    '@remaply/validators': private
  param-case@2.1.1:
    param-case: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parse-png@2.1.0:
    parse-png: private
  parse-srcset@1.0.2:
    parse-srcset: private
  parseurl@1.3.3:
    parseurl: private
  pascal-case@2.0.1:
    pascal-case: private
  path-case@2.1.1:
    path-case: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@2.0.0:
    path-scurry: private
  path-type@4.0.0:
    path-type: private
  pg-cloudflare@1.2.7:
    pg-cloudflare: private
  pg-connection-string@2.9.1:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-numeric@1.0.2:
    pg-numeric: private
  pg-pool@3.10.1(pg@8.16.3):
    pg-pool: private
  pg-protocol@1.10.3:
    pg-protocol: private
  pg-types@4.0.2:
    pg-types: private
  pg@8.16.3:
    pg: private
  pgpass@1.0.5:
    pgpass: private
  picocolors@1.0.1:
    picocolors: private
  picomatch@3.0.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@4.2.0:
    pkg-dir: private
  plist@3.1.0:
    plist: private
  pngjs@3.4.0:
    pngjs: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-import@15.1.0(postcss@8.5.6):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.6):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.6)(ts-node@10.9.2(@types/node@22.16.3)(typescript@5.8.3)):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.6):
    postcss-nested: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  postgres-range@1.1.4:
    postgres-range: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-plugin-tailwindcss@0.6.14(@ianvs/prettier-plugin-sort-imports@4.5.1(prettier@3.6.2))(prettier@3.6.2):
    prettier-plugin-tailwindcss: private
  pretty-bytes@5.6.0:
    pretty-bytes: private
  pretty-format@29.7.0:
    pretty-format: private
  proc-log@4.2.0:
    proc-log: private
  progress@2.0.3:
    progress: private
  promise@8.3.0:
    promise: private
  prompts@2.4.2:
    prompts: private
  prop-types@15.8.1:
    prop-types: private
  property-expr@2.0.6:
    property-expr: private
  proxy-agent@6.5.0:
    proxy-agent: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  pure-rand@6.1.0:
    pure-rand: private
  pvtsutils@1.3.6:
    pvtsutils: private
  pvutils@1.1.3:
    pvutils: private
  qrcode-terminal@0.11.0:
    qrcode-terminal: private
  query-string@7.1.3:
    query-string: private
  queue-microtask@1.2.3:
    queue-microtask: private
  queue@6.0.2:
    queue: private
  radix-ui@1.4.2(@types/react-dom@19.1.6(@types/react@19.1.8))(@types/react@19.1.8)(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    radix-ui: private
  range-parser@1.2.1:
    range-parser: private
  rc@1.2.8:
    rc: private
  react-devtools-core@6.1.5(bufferutil@4.0.9):
    react-devtools-core: private
  react-fast-compare@2.0.4:
    react-fast-compare: private
  react-freeze@1.0.4(react@19.0.0):
    react-freeze: private
  react-hook-form@7.60.0(react@19.0.0):
    react-hook-form: private
  react-is@16.13.1:
    react-is: private
  react-native-css-interop@0.1.22(react-native-reanimated@3.17.5(@babel/core@7.28.0)(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.4.0(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0))(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0)(tailwindcss@3.4.17(ts-node@10.9.2(@types/node@22.16.3)(typescript@5.8.3))):
    react-native-css-interop: private
  react-native-edge-to-edge@1.6.0(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0):
    react-native-edge-to-edge: private
  react-native-gesture-handler@2.24.0(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0):
    react-native-gesture-handler: private
  react-native-is-edge-to-edge@1.2.1(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0):
    react-native-is-edge-to-edge: private
  react-native-reanimated@3.17.5(@babel/core@7.28.0)(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0):
    react-native-reanimated: private
  react-native-safe-area-context@5.4.0(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0):
    react-native-safe-area-context: private
  react-native-screens@4.11.1(react-native@0.79.5(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0))(react@19.0.0):
    react-native-screens: private
  react-native-web@0.20.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    react-native-web: private
  react-native@0.80.1(@babel/core@7.28.0)(@types/react@19.1.8)(bufferutil@4.0.9)(react@19.0.0):
    react-native: private
  react-refresh@0.14.2:
    react-refresh: private
  react-remove-scroll-bar@2.3.8(@types/react@19.1.8)(react@19.0.0):
    react-remove-scroll-bar: private
  react-remove-scroll@2.7.1(@types/react@19.1.8)(react@19.0.0):
    react-remove-scroll: private
  react-style-singleton@2.2.3(@types/react@19.1.8)(react@19.0.0):
    react-style-singleton: private
  read-cache@1.0.0:
    read-cache: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regenerator-runtime@0.13.11:
    regenerator-runtime: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regexpu-core@6.2.0:
    regexpu-core: private
  registry-auth-token@3.3.2:
    registry-auth-token: private
  registry-url@3.1.0:
    registry-url: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  repeat-string@1.6.1:
    repeat-string: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  requireg@0.2.2:
    requireg: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve-workspace-root@2.0.0:
    resolve-workspace-root: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@2.0.0-next.5:
    resolve: private
  restore-cursor@3.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rimraf@6.0.1:
    rimraf: private
  rou3@0.5.1:
    rou3: private
  run-async@2.4.1:
    run-async: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sanitize-html@2.17.0:
    sanitize-html: private
  sax@1.4.1:
    sax: private
  scheduler@0.25.0:
    scheduler: private
  semver@6.3.1:
    semver: private
  send@0.19.1:
    send: private
  sentence-case@2.1.1:
    sentence-case: private
  serialize-error@2.1.0:
    serialize-error: private
  serve-static@1.16.2:
    serve-static: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setimmediate@1.0.5:
    setimmediate: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shared:
    '@remaply/shared': private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-plist@1.3.1:
    simple-plist: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  slugify@1.6.6:
    slugify: private
  smart-buffer@4.2.0:
    smart-buffer: private
  snake-case@2.1.0:
    snake-case: private
  socks-proxy-agent@8.0.5:
    socks-proxy-agent: private
  socks@2.8.6:
    socks: private
  sonner@2.0.6(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    sonner: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.5.7:
    source-map: private
  spawn-command@0.0.2:
    spawn-command: private
  split-on-first@1.1.0:
    split-on-first: private
  split2@4.2.0:
    split2: private
  sprintf-js@1.1.3:
    sprintf-js: private
  stable-hash@0.0.5:
    stable-hash: private
  stack-utils@2.0.6:
    stack-utils: private
  stackframe@1.3.4:
    stackframe: private
  stacktrace-parser@0.1.11:
    stacktrace-parser: private
  statuses@2.0.1:
    statuses: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  stream-buffers@2.2.0:
    stream-buffers: private
  streamsearch@1.1.0:
    streamsearch: private
  strict-uri-encode@2.0.0:
    strict-uri-encode: private
  string-length@4.0.2:
    string-length: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  structured-headers@0.4.1:
    structured-headers: private
  styled-jsx@5.1.1(@babel/core@7.28.0)(react@19.0.0):
    styled-jsx: private
  styleq@0.1.3:
    styleq: private
  sucrase@3.35.0:
    sucrase: private
  superjson@2.2.2:
    superjson: private
  supports-color@8.1.1:
    supports-color: private
  supports-hyperlinks@2.3.0:
    supports-hyperlinks: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swap-case@1.1.2:
    swap-case: private
  tailwind-merge@3.3.1:
    tailwind-merge: private
  tailwindcss-animate@1.0.7(tailwindcss@3.4.17(ts-node@10.9.2(@types/node@22.16.3)(typescript@5.8.3))):
    tailwindcss-animate: private
  tailwindcss@3.4.17(ts-node@10.9.2(@types/node@22.16.3)(typescript@5.8.3)):
    tailwindcss: private
  tar@7.4.3:
    tar: private
  temp-dir@2.0.0:
    temp-dir: private
  terminal-link@2.1.1:
    terminal-link: private
  terser@5.43.1:
    terser: private
  test-exclude@6.0.0:
    test-exclude: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  throat@5.0.0:
    throat: private
  through@2.3.8:
    through: private
  tiny-case@1.0.3:
    tiny-case: private
  tiny-warning@1.0.3:
    tiny-warning: private
  tinycolor2@1.6.0:
    tinycolor2: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tinygradient@1.1.5:
    tinygradient: private
  title-case@2.1.1:
    title-case: private
  tmp@0.0.33:
    tmp: private
  tmpl@1.0.5:
    tmpl: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  tooling/eslint:
    '@remaply/eslint-config': private
  tooling/github:
    '@remaply/github': private
  tooling/tailwind:
    '@remaply/tailwind-config': private
  tooling/typescript:
    '@remaply/tsconfig': private
  toposort@2.0.2:
    toposort: private
  tr46@0.0.3:
    tr46: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  ts-jest@29.4.0(@babel/core@7.28.0)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.28.0))(jest-util@29.7.0)(jest@29.7.0(@types/node@22.16.3)(ts-node@10.9.2(@types/node@22.16.3)(typescript@5.8.3)))(typescript@5.8.3):
    ts-jest: private
  ts-node@10.9.2(@types/node@22.16.3)(typescript@5.8.3):
    ts-node: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  turbo-darwin-64@2.5.4:
    turbo-darwin-64: private
  turbo-darwin-arm64@2.5.4:
    turbo-darwin-arm64: private
  turbo-linux-64@2.5.4:
    turbo-linux-64: private
  turbo-linux-arm64@2.5.4:
    turbo-linux-arm64: private
  turbo-windows-64@2.5.4:
    turbo-windows-64: private
  turbo-windows-arm64@2.5.4:
    turbo-windows-arm64: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@4.41.0:
    type-fest: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typescript-eslint@8.36.0(eslint@9.31.0(jiti@1.21.7))(typescript@5.8.3):
    typescript-eslint: private
  ua-parser-js@1.0.40:
    ua-parser-js: private
  uglify-js@3.19.3:
    uglify-js: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  uncrypto@0.1.3:
    uncrypto: private
  undici-types@6.21.0:
    undici-types: private
  undici@6.21.3:
    undici: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  unique-string@2.0.0:
    unique-string: private
  universalify@2.0.1:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  unrs-resolver@1.11.1:
    unrs-resolver: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  update-check@1.5.4:
    update-check: private
  upper-case-first@1.1.2:
    upper-case-first: private
  upper-case@1.1.3:
    upper-case: private
  uri-js@4.4.1:
    uri-js: private
  use-callback-ref@1.3.3(@types/react@19.1.8)(react@19.0.0):
    use-callback-ref: private
  use-latest-callback@0.2.4(react@19.0.0):
    use-latest-callback: private
  use-sidecar@1.1.3(@types/react@19.1.8)(react@19.0.0):
    use-sidecar: private
  use-sync-external-store@1.5.0(react@19.0.0):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@10.0.0:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  v8-to-istanbul@9.3.0:
    v8-to-istanbul: private
  validate-npm-package-name@5.0.1:
    validate-npm-package-name: private
  vary@1.1.2:
    vary: private
  vlq@1.0.1:
    vlq: private
  walker@1.0.8:
    walker: private
  warn-once@0.1.1:
    warn-once: private
  watchpack@2.4.0:
    watchpack: private
  wcwidth@1.0.1:
    wcwidth: private
  web:
    web: private
  webidl-conversions@5.0.0:
    webidl-conversions: private
  whatwg-fetch@3.6.20:
    whatwg-fetch: private
  whatwg-url-without-unicode@8.0.0-3:
    whatwg-url-without-unicode: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  wonka@6.3.5:
    wonka: private
  word-wrap@1.2.5:
    word-wrap: private
  wordwrap@1.0.0:
    wordwrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@4.0.2:
    write-file-atomic: private
  ws@8.18.3(bufferutil@4.0.9):
    ws: private
  xcode@3.0.1:
    xcode: private
  xml2js@0.6.0:
    xml2js: private
  xmlbuilder@15.1.1:
    xmlbuilder: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
  yup@1.6.1:
    yup: private
  zod-validation-error@3.5.3(zod@3.25.76):
    zod-validation-error: private
  zod@3.25.76:
    zod: private
ignoredBuilds:
  - core-js-pure
  - bufferutil
  - unrs-resolver
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps:
  frontend-shared:
    - node_modules\.pnpm\@remaply+frontend-shared@fi_e68a5d25b696f08fa17462a7a2170c39\node_modules\@remaply\frontend-shared
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.1
pendingBuilds: []
prunedAt: Mon, 14 Jul 2025 03:05:32 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.4'
  - '@emnapi/runtime@1.4.4'
  - '@emnapi/wasi-threads@1.0.3'
  - '@esbuild/aix-ppc64@0.25.6'
  - '@esbuild/android-arm64@0.18.20'
  - '@esbuild/android-arm64@0.25.6'
  - '@esbuild/android-arm@0.18.20'
  - '@esbuild/android-arm@0.25.6'
  - '@esbuild/android-x64@0.18.20'
  - '@esbuild/android-x64@0.25.6'
  - '@esbuild/darwin-arm64@0.18.20'
  - '@esbuild/darwin-arm64@0.25.6'
  - '@esbuild/darwin-x64@0.18.20'
  - '@esbuild/darwin-x64@0.25.6'
  - '@esbuild/freebsd-arm64@0.18.20'
  - '@esbuild/freebsd-arm64@0.25.6'
  - '@esbuild/freebsd-x64@0.18.20'
  - '@esbuild/freebsd-x64@0.25.6'
  - '@esbuild/linux-arm64@0.18.20'
  - '@esbuild/linux-arm64@0.25.6'
  - '@esbuild/linux-arm@0.18.20'
  - '@esbuild/linux-arm@0.25.6'
  - '@esbuild/linux-ia32@0.18.20'
  - '@esbuild/linux-ia32@0.25.6'
  - '@esbuild/linux-loong64@0.18.20'
  - '@esbuild/linux-loong64@0.25.6'
  - '@esbuild/linux-mips64el@0.18.20'
  - '@esbuild/linux-mips64el@0.25.6'
  - '@esbuild/linux-ppc64@0.18.20'
  - '@esbuild/linux-ppc64@0.25.6'
  - '@esbuild/linux-riscv64@0.18.20'
  - '@esbuild/linux-riscv64@0.25.6'
  - '@esbuild/linux-s390x@0.18.20'
  - '@esbuild/linux-s390x@0.25.6'
  - '@esbuild/linux-x64@0.18.20'
  - '@esbuild/linux-x64@0.25.6'
  - '@esbuild/netbsd-arm64@0.25.6'
  - '@esbuild/netbsd-x64@0.18.20'
  - '@esbuild/netbsd-x64@0.25.6'
  - '@esbuild/openbsd-arm64@0.25.6'
  - '@esbuild/openbsd-x64@0.18.20'
  - '@esbuild/openbsd-x64@0.25.6'
  - '@esbuild/openharmony-arm64@0.25.6'
  - '@esbuild/sunos-x64@0.18.20'
  - '@esbuild/sunos-x64@0.25.6'
  - '@esbuild/win32-arm64@0.18.20'
  - '@esbuild/win32-arm64@0.25.6'
  - '@esbuild/win32-ia32@0.18.20'
  - '@esbuild/win32-ia32@0.25.6'
  - '@img/sharp-darwin-arm64@0.34.3'
  - '@img/sharp-darwin-x64@0.34.3'
  - '@img/sharp-libvips-darwin-arm64@1.2.0'
  - '@img/sharp-libvips-darwin-x64@1.2.0'
  - '@img/sharp-libvips-linux-arm64@1.2.0'
  - '@img/sharp-libvips-linux-arm@1.2.0'
  - '@img/sharp-libvips-linux-ppc64@1.2.0'
  - '@img/sharp-libvips-linux-s390x@1.2.0'
  - '@img/sharp-libvips-linux-x64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.2.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.2.0'
  - '@img/sharp-linux-arm64@0.34.3'
  - '@img/sharp-linux-arm@0.34.3'
  - '@img/sharp-linux-ppc64@0.34.3'
  - '@img/sharp-linux-s390x@0.34.3'
  - '@img/sharp-linux-x64@0.34.3'
  - '@img/sharp-linuxmusl-arm64@0.34.3'
  - '@img/sharp-linuxmusl-x64@0.34.3'
  - '@img/sharp-wasm32@0.34.3'
  - '@img/sharp-win32-arm64@0.34.3'
  - '@img/sharp-win32-ia32@0.34.3'
  - '@napi-rs/wasm-runtime@0.2.12'
  - '@next/swc-darwin-arm64@14.0.4'
  - '@next/swc-darwin-x64@14.0.4'
  - '@next/swc-linux-arm64-gnu@14.0.4'
  - '@next/swc-linux-arm64-musl@14.0.4'
  - '@next/swc-linux-x64-gnu@14.0.4'
  - '@next/swc-linux-x64-musl@14.0.4'
  - '@next/swc-win32-arm64-msvc@14.0.4'
  - '@next/swc-win32-ia32-msvc@14.0.4'
  - '@tybys/wasm-util@0.10.0'
  - '@unrs/resolver-binding-android-arm-eabi@1.11.1'
  - '@unrs/resolver-binding-android-arm64@1.11.1'
  - '@unrs/resolver-binding-darwin-arm64@1.11.1'
  - '@unrs/resolver-binding-darwin-x64@1.11.1'
  - '@unrs/resolver-binding-freebsd-x64@1.11.1'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-musl@1.11.1'
  - '@unrs/resolver-binding-wasm32-wasi@1.11.1'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.11.1'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.11.1'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.27.0
  - lightningcss-darwin-x64@1.27.0
  - lightningcss-freebsd-x64@1.27.0
  - lightningcss-linux-arm-gnueabihf@1.27.0
  - lightningcss-linux-arm64-gnu@1.27.0
  - lightningcss-linux-arm64-musl@1.27.0
  - lightningcss-linux-x64-gnu@1.27.0
  - lightningcss-linux-x64-musl@1.27.0
  - lightningcss-win32-arm64-msvc@1.27.0
  - turbo-darwin-64@2.5.4
  - turbo-darwin-arm64@2.5.4
  - turbo-linux-64@2.5.4
  - turbo-linux-arm64@2.5.4
  - turbo-windows-arm64@2.5.4
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Desktop\projectX\Remaply\node_modules\.pnpm
virtualStoreDirMaxLength: 60
