#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/chrome-launcher@0.15.2/node_modules/chrome-launcher/bin/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/chrome-launcher@0.15.2/node_modules/chrome-launcher/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/chrome-launcher@0.15.2/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/chrome-launcher@0.15.2/node_modules/chrome-launcher/bin/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/chrome-launcher@0.15.2/node_modules/chrome-launcher/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/chrome-launcher@0.15.2/node_modules:/mnt/c/Users/<USER>/Desktop/projectX/Remaply/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../chrome-launcher/bin/print-chrome-path.js" "$@"
else
  exec node  "$basedir/../chrome-launcher/bin/print-chrome-path.js" "$@"
fi
