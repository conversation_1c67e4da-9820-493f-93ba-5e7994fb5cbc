import React from 'react';
import {
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  StatusBar,
  TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Button, Input, Text, Icon } from '@remaply/frontend-shared';
import { LoginScreenProps } from './types';
import { useLoginScreen } from './useLoginScreen';

const LoginScreen: React.FC<LoginScreenProps> = ({ navigation }) => {
  const {
    state,
    validationErrors,
    handleGoogleSignIn,
    handleEmailSignIn,
    handleBack,
    handleToggleMode,
    handleEmailChange,
    handlePasswordChange,
  } = useLoginScreen();

  return (
    <View className="flex-1">
      <StatusBar
        barStyle="dark-content"
        backgroundColor="transparent"
        translucent
      />

      <LinearGradient
        colors={['#F9FAFB', '#FFFFFF']}
        className="flex-1"
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          className="flex-1"
        >
          <ScrollView
            contentContainerStyle={{ flexGrow: 1 }}
            keyboardShouldPersistTaps="handled"
            className="px-6"
          >
            {/* Header */}
            <View className="flex-row items-center pt-15 pb-6">
              <TouchableOpacity onPress={handleBack} className="p-2">
                <Icon name="back" size={24} color="#374151" />
              </TouchableOpacity>
            </View>

            {/* Welcome Header */}
            <View className="items-center mb-10">
              <Text
                variant="h1"
                className="text-3xl font-bold text-gray-900 mb-2 text-center"
                children={state.isSignUp ? "Create Account" : "Welcome Back"}
              />
              <Text
                variant="body1"
                className="text-base text-gray-600 text-center"
                children={state.isSignUp ? "Join Remaply and start managing your social media" : "Sign in to your Remaply account"}
              />
            </View>

            {/* Google Sign In Button */}
            <View className="mb-6">
              <Button
                variant="outline"
                size="large"
                fullWidth
                onPress={handleGoogleSignIn}
                loading={state.isLoading}
                style={{ backgroundColor: '#FFFFFF', borderWidth: 1, borderColor: '#E5E7EB', borderRadius: 12 }}
                children={
                  <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                    <Icon name="google" size={20} color="#374151" />
                    <Text style={{ marginLeft: 8, color: '#374151', fontWeight: '500' }}>
                      Continue with Google
                    </Text>
                  </View>
                }
              />
            </View>

            {/* Divider */}
            <View className="flex-row items-center mb-6">
              <View className="flex-1 h-px bg-gray-200" />
              <Text className="mx-4 text-sm text-gray-500">or</Text>
              <View className="flex-1 h-px bg-gray-200" />
            </View>

            {/* Form */}
            <View className="flex-1">
              <Input
                label="Email"
                placeholder="Enter your email"
                value={state.formData.email}
                onChangeText={handleEmailChange}
                error={validationErrors.email}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                className="mb-4 bg-white border border-gray-200 rounded-lg"
              />

              <Input
                label="Password"
                placeholder="Enter your password"
                value={state.formData.password}
                onChangeText={handlePasswordChange}
                error={validationErrors.password}
                secureTextEntry
                className="mb-4 bg-white border border-gray-200 rounded-lg"
              />

              {!state.isSignUp && (
                <Button
                  variant="text"
                  onPress={() => console.log('Forgot password')}
                  style={{ alignSelf: 'flex-end', marginBottom: 24 }}
                  children={
                    <Text
                      variant="body2"
                      style={{ color: '#4F46E5', fontWeight: '500' }}
                      children="Forgot Password?"
                    />
                  }
                />
              )}

              <Button
                variant="primary"
                size="large"
                fullWidth
                onPress={handleEmailSignIn}
                loading={state.isLoading}
                style={{ borderRadius: 12, shadowColor: '#6C63FF', shadowOffset: { width: 0, height: 4 }, shadowOpacity: 0.3, shadowRadius: 8, elevation: 8, marginBottom: 24 }}
                children={state.isSignUp ? "Create Account" : "Sign In"}
              />

              {/* Error Message */}
              {state.error && (
                <View className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                  <Text className="text-red-600 text-sm text-center">
                    {state.error}
                  </Text>
                </View>
              )}
            </View>

            {/* Footer */}
            <View className="flex-row justify-center items-center pb-8">
              <Text
                variant="body2"
                className="text-gray-600"
                children={state.isSignUp ? "Already have an account? " : "Don't have an account? "}
              />
              <Button
                variant="text"
                onPress={handleToggleMode}
                children={
                  <Text
                    variant="body2"
                    className="text-indigo-600 font-semibold"
                    children={state.isSignUp ? "Sign In" : "Sign Up"}
                  />
                }
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </LinearGradient>
    </View>
  );
};

export default LoginScreen; 