{"version": 3, "file": "safe-area.js", "sourceRoot": "", "sources": ["../../src/tailwind/safe-area.ts"], "names": [], "mappings": ";;;;;;AA4BA,gEAAwC;AAExC,SAAS,wBAAwB,CAC/B,SAA+B,EAC/B,OAAe,EACf,aAA+C;IAE/C,OAAO,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,CACrC,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC,EAAE,EAAE;QACjC,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,GAAG,CAAC,GAAG,SAAS,IAAI,OAAO,EAAE,CAAC,GAAG,CAAC,CAAS,EAAE,EAAE,CAC7C,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,MAAM,CAClC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,EAAE;YAC5B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CACjC,CAAC,KAAK,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CACzD,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAC7C,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,EAAuC,CACxC,CAAC;QACJ,OAAO,GAAG,CAAC;IACb,CAAC,EACD,EAAsE,CACvE,CAAC;AACJ,CAAC;AAED,MAAM,aAAa,GAAG;IACpB,SAAS,EAAE;QACT,SAAS,EAAE,0BAA0B;QACrC,WAAW,EAAE,4BAA4B;QACzC,YAAY,EAAE,6BAA6B;QAC3C,UAAU,EAAE,2BAA2B;KACxC;IACD,UAAU,EAAE;QACV,WAAW,EAAE,4BAA4B;QACzC,UAAU,EAAE,2BAA2B;KACxC;IACD,UAAU,EAAE;QACV,SAAS,EAAE,0BAA0B;QACrC,YAAY,EAAE,6BAA6B;KAC5C;IACD,UAAU,EAAE;QACV,iBAAiB,EAAE,2BAA2B;KAC/C;IACD,UAAU,EAAE;QACV,eAAe,EAAE,2BAA2B;KAC7C;IACD,UAAU,EAAE;QACV,SAAS,EAAE,0BAA0B;KACtC;IACD,UAAU,EAAE;QACV,WAAW,EAAE,4BAA4B;KAC1C;IACD,UAAU,EAAE;QACV,YAAY,EAAE,6BAA6B;KAC5C;IACD,UAAU,EAAE;QACV,UAAU,EAAE,2BAA2B;KACxC;IACD,SAAS,EAAE;QACT,UAAU,EAAE,0BAA0B;QACtC,YAAY,EAAE,4BAA4B;QAC1C,aAAa,EAAE,6BAA6B;QAC5C,WAAW,EAAE,2BAA2B;KACzC;IACD,UAAU,EAAE;QACV,YAAY,EAAE,4BAA4B;QAC1C,WAAW,EAAE,2BAA2B;KACzC;IACD,UAAU,EAAE;QACV,UAAU,EAAE,0BAA0B;QACtC,aAAa,EAAE,6BAA6B;KAC7C;IACD,UAAU,EAAE;QACV,kBAAkB,EAAE,2BAA2B;KAChD;IACD,UAAU,EAAE;QACV,gBAAgB,EAAE,2BAA2B;KAC9C;IACD,UAAU,EAAE;QACV,UAAU,EAAE,0BAA0B;KACvC;IACD,UAAU,EAAE;QACV,YAAY,EAAE,4BAA4B;KAC3C;IACD,UAAU,EAAE;QACV,aAAa,EAAE,6BAA6B;KAC7C;IACD,UAAU,EAAE;QACV,WAAW,EAAE,2BAA2B;KACzC;IACD,gBAAgB,EAAE;QAChB,eAAe,EAAE,0BAA0B;QAC3C,iBAAiB,EAAE,4BAA4B;QAC/C,kBAAkB,EAAE,6BAA6B;QACjD,gBAAgB,EAAE,2BAA2B;KAC9C;IACD,iBAAiB,EAAE;QACjB,iBAAiB,EAAE,4BAA4B;QAC/C,gBAAgB,EAAE,2BAA2B;KAC9C;IACD,iBAAiB,EAAE;QACjB,eAAe,EAAE,0BAA0B;QAC3C,kBAAkB,EAAE,6BAA6B;KAClD;IACD,iBAAiB,EAAE;QACjB,uBAAuB,EAAE,2BAA2B;KACrD;IACD,iBAAiB,EAAE;QACjB,qBAAqB,EAAE,2BAA2B;KACnD;IACD,iBAAiB,EAAE;QACjB,eAAe,EAAE,0BAA0B;KAC5C;IACD,iBAAiB,EAAE;QACjB,iBAAiB,EAAE,4BAA4B;KAChD;IACD,iBAAiB,EAAE;QACjB,kBAAkB,EAAE,6BAA6B;KAClD;IACD,iBAAiB,EAAE;QACjB,gBAAgB,EAAE,2BAA2B;KAC9C;IACD,gBAAgB,EAAE;QAChB,gBAAgB,EAAE,0BAA0B;QAC5C,kBAAkB,EAAE,4BAA4B;QAChD,mBAAmB,EAAE,6BAA6B;QAClD,iBAAiB,EAAE,2BAA2B;KAC/C;IACD,iBAAiB,EAAE;QACjB,kBAAkB,EAAE,4BAA4B;QAChD,iBAAiB,EAAE,2BAA2B;KAC/C;IACD,iBAAiB,EAAE;QACjB,gBAAgB,EAAE,0BAA0B;QAC5C,mBAAmB,EAAE,6BAA6B;KACnD;IACD,iBAAiB,EAAE;QACjB,wBAAwB,EAAE,2BAA2B;KACtD;IACD,iBAAiB,EAAE;QACjB,sBAAsB,EAAE,2BAA2B;KACpD;IACD,iBAAiB,EAAE;QACjB,gBAAgB,EAAE,0BAA0B;KAC7C;IACD,iBAAiB,EAAE;QACjB,kBAAkB,EAAE,4BAA4B;KACjD;IACD,iBAAiB,EAAE;QACjB,mBAAmB,EAAE,6BAA6B;KACnD;IACD,iBAAiB,EAAE;QACjB,iBAAiB,EAAE,2BAA2B;KAC/C;IACD,aAAa,EAAE;QACb,GAAG,EAAE,0BAA0B;QAC/B,KAAK,EAAE,4BAA4B;QACnC,MAAM,EAAE,6BAA6B;QACrC,IAAI,EAAE,2BAA2B;KAClC;IACD,eAAe,EAAE;QACf,KAAK,EAAE,4BAA4B;QACnC,IAAI,EAAE,2BAA2B;KAClC;IACD,eAAe,EAAE;QACf,GAAG,EAAE,0BAA0B;QAC/B,MAAM,EAAE,6BAA6B;KACtC;IACD,aAAa,EAAE;QACb,gBAAgB,EAAE,2BAA2B;KAC9C;IACD,WAAW,EAAE;QACX,cAAc,EAAE,2BAA2B;KAC5C;IACD,WAAW,EAAE;QACX,GAAG,EAAE,0BAA0B;KAChC;IACD,aAAa,EAAE;QACb,KAAK,EAAE,4BAA4B;KACpC;IACD,cAAc,EAAE;QACd,MAAM,EAAE,6BAA6B;KACtC;IACD,YAAY,EAAE;QACZ,IAAI,EAAE,2BAA2B;KAClC;IACD,oBAAoB,EAAE;QACpB,SAAS,EAAE;YACT,wEAAwE;YACxE,wBAAwB;SACzB;KACF;IACD,oBAAoB,EAAE;QACpB,SAAS,EAAE;YACT,wEAAwE;YACxE,wBAAwB;SACzB;KACF;IACD,gBAAgB,EAAE;QAChB,MAAM,EAAE;YACN,wEAAwE;YACxE,wBAAwB;SACzB;KACF;CACF,CAAC;AAEW,QAAA,QAAQ,GAAG,IAAA,gBAAM,EAAC,CAAC,EAAE,YAAY,EAAE,cAAc,EAAE,KAAK,EAAE,EAAE,EAAE;IACzE,YAAY,CAAC,aAAa,CAAC,CAAC;IAE5B,MAAM,eAAe,GAAG,wBAAwB,CAC9C,aAAa,EACb,QAAQ,EACR,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAC9B,CAAC;IACF,cAAc,CAAC,eAAe,EAAE;QAC9B,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC;QACxB,sBAAsB,EAAE,IAAI;KAC7B,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,wBAAwB,CAC1C,aAAa,EACb,IAAI,EACJ,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAC5B,CAAC;IACF,cAAc,CAAC,WAAW,EAAE;QAC1B,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC;QACxB,sBAAsB,EAAE,IAAI;KAC7B,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}