import React from 'react';
import { View, StyleSheet, StatusBar, Animated } from 'react-native';
import { Button, Text, useTheme } from '@remaply/frontend-shared';
import { WelcomeScreenProps } from './types';
import { useWelcomeScreen } from './useWelcomeScreen';

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ navigation }) => {
  const { animationValues, handleGetStarted, handleLogin } = useWelcomeScreen();
  const theme = useTheme();

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={theme.colors.background.default}
        translucent={false}
      />

      {/* Main Content */}
      <View style={styles.content}>
        {/* Logo Section */}
        <Animated.View
          style={[
            styles.logoContainer,
            {
              opacity: animationValues.logoOpacity,
              transform: [{ scale: animationValues.logoScale }],
            }
          ]}
        >
          <View style={styles.logoWrapper}>
            {/* Remaply Logo - Using geometric shapes to represent the logo */}
            <View style={styles.logoShape1} />
            <View style={styles.logoShape2} />
            <View style={styles.logoShape3} />
          </View>
        </Animated.View>

        {/* Title Section */}
        <Animated.View
          style={[
            styles.titleContainer,
            { opacity: animationValues.titleOpacity }
          ]}
        >
          <Text
            variant="h1"
            style={styles.title}
            children="Welcome to Remaply: Your Smart Business Assistant"
          />
        </Animated.View>

        {/* Subtitle Section */}
        <Animated.View
          style={[
            styles.subtitleContainer,
            { opacity: animationValues.subtitleOpacity }
          ]}
        >
          <Text
            variant="body1"
            style={styles.subtitle}
            children="Create. Schedule. Auto-Reply. Let AI run your brand."
          />
        </Animated.View>

        {/* Progress Dots */}
        <View style={styles.progressContainer}>
          <View style={[styles.progressDot, styles.progressDotActive]} />
          <View style={styles.progressDot} />
        </View>
      </View>

      {/* Bottom Section */}
      <Animated.View
        style={[
          styles.bottomContainer,
          {
            opacity: animationValues.buttonsOpacity,
            transform: [{ translateY: animationValues.buttonsTranslateY }],
          }
        ]}
      >
        <Button
          variant="primary"
          size="medium"
          fullWidth
          onPress={handleGetStarted}
          style={styles.getStartedButton}
          children="Get Started"
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFF9F6', // Soft Cream background
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingTop: 60,
  },
  logoContainer: {
    marginBottom: 48,
  },
  logoWrapper: {
    width: 80,
    height: 80,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoShape1: {
    position: 'absolute',
    width: 32,
    height: 32,
    backgroundColor: '#FF7CA3', // Blush Pink
    borderRadius: 8,
    top: 8,
    left: 8,
    transform: [{ rotate: '45deg' }],
  },
  logoShape2: {
    position: 'absolute',
    width: 24,
    height: 24,
    backgroundColor: '#FFA987', // Coral Peach
    borderRadius: 6,
    top: 16,
    right: 8,
    transform: [{ rotate: '15deg' }],
  },
  logoShape3: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: '#FF3B30', // Warm Red
    borderRadius: 4,
    bottom: 12,
    left: 16,
    transform: [{ rotate: '-30deg' }],
  },
  titleContainer: {
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#2C2C2C', // Charcoal Gray
    textAlign: 'center',
    lineHeight: 36,
  },
  subtitleContainer: {
    marginBottom: 48,
    paddingHorizontal: 24,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: '400',
    color: '#6E6E6E', // Muted Gray
    textAlign: 'center',
    lineHeight: 24,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#EAEAEA', // Mist Gray
    marginHorizontal: 4,
  },
  progressDotActive: {
    backgroundColor: '#2C2C2C', // Charcoal Gray
  },
  bottomContainer: {
    paddingHorizontal: 32,
    paddingBottom: 48,
  },
  getStartedButton: {
    backgroundColor: '#2C2C2C', // Charcoal Gray (following the design)
    borderRadius: 12,
    paddingVertical: 16,
  },
});

export default WelcomeScreen;