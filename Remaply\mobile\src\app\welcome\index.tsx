import React from 'react';
import { View, StatusBar, Animated } from 'react-native';
import { Button, Text } from '@remaply/frontend-shared';
import { WelcomeScreenProps } from './types';
import { useWelcomeScreen } from './useWelcomeScreen';
import { welcomeStyles } from './styles';

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ navigation }) => {
  const { animationValues, handleGetStarted } = useWelcomeScreen();

  return (
    <View style={welcomeStyles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor="#FFF9F6"
        translucent={false}
      />

      {/* Main Content */}
      <View style={welcomeStyles.content}>
        {/* Logo Section */}
        <Animated.View
          style={[
            welcomeStyles.logoContainer,
            {
              opacity: animationValues.logoOpacity,
              transform: [{ scale: animationValues.logoScale }],
            }
          ]}
        >
          <View style={welcomeStyles.logoWrapper}>
            {/* Remaply Logo - Using geometric shapes to represent the logo */}
            <View style={welcomeStyles.logoShape1} />
            <View style={welcomeStyles.logoShape2} />
            <View style={welcomeStyles.logoShape3} />
          </View>
        </Animated.View>

        {/* Title Section */}
        <Animated.View
          style={[
            welcomeStyles.titleContainer,
            { opacity: animationValues.titleOpacity }
          ]}
        >
          <Text
            variant="h1"
            style={welcomeStyles.title}
            children="Welcome to Remaply: Your Smart Business Assistant"
          />
        </Animated.View>

        {/* Subtitle Section */}
        <Animated.View
          style={[
            welcomeStyles.subtitleContainer,
            { opacity: animationValues.subtitleOpacity }
          ]}
        >
          <Text
            variant="body1"
            style={welcomeStyles.subtitle}
            children="Create. Schedule. Auto-Reply. Let AI run your brand."
          />
        </Animated.View>

        {/* Progress Dots */}
        <View style={welcomeStyles.progressContainer}>
          <View style={[welcomeStyles.progressDot, welcomeStyles.progressDotActive]} />
          <View style={welcomeStyles.progressDot} />
        </View>
      </View>

      {/* Bottom Section */}
      <Animated.View
        style={[
          welcomeStyles.bottomContainer,
          {
            opacity: animationValues.buttonsOpacity,
            transform: [{ translateY: animationValues.buttonsTranslateY }],
          }
        ]}
      >
        <Button
          variant="primary"
          size="medium"
          fullWidth
          onPress={handleGetStarted}
          style={welcomeStyles.getStartedButton}
          children="Get Started"
        />
      </Animated.View>
    </View>
  );
};

export default WelcomeScreen;