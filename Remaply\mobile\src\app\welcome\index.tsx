import React, { useEffect, useRef } from "react"
import { View, StatusBar, Dimensions, Animated, StyleSheet } from "react-native"
import { Text } from "@remaply/frontend-shared"
import NeuralLogo from "./logo"

const { width: screenWidth, height: screenHeight } = Dimensions.get("window")

interface WelcomeScreenProps {
  navigation: any
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ navigation }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current
  const slideAnim = useRef(new Animated.Value(30)).current

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start()

    // Navigate to onboarding after 6 seconds
    const timer = setTimeout(() => {
      // navigation.navigate('Onboarding') // Uncomment when onboarding is ready
      console.log("Navigate to onboarding")
    }, 6000)

    return () => clearTimeout(timer)
  }, [navigation])

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" translucent={false} />

      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          }
        ]}
      >
        {/* Logo */}
        <View style={styles.logoContainer}>
          <NeuralLogo size={140} interactive={false} />
        </View>

        {/* Brand Name */}
        <Animated.View style={styles.brandContainer}>
          <Text style={styles.brandName}>Remaply</Text>
        </Animated.View>

        {/* Tagline */}
        <Animated.View style={styles.taglineContainer}>
          <Text style={styles.tagline}>Your AI-powered business management</Text>
        </Animated.View>
      </Animated.View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF", // Pure white background
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 40,
  },
  logoContainer: {
    marginBottom: 40,
  },
  brandContainer: {
    marginBottom: 16,
  },
  brandName: {
    fontSize: 42,
    fontWeight: "800" as const,
    color: "#2C2C2C", // Dark gray
    letterSpacing: -1,
    textAlign: "center" as const,
    fontFamily: "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
  },
  taglineContainer: {
    paddingHorizontal: 20,
  },
  tagline: {
    fontSize: 18,
    fontWeight: "400" as const,
    color: "#6E6E6E", // Medium gray
    letterSpacing: 0.2,
    textAlign: "center" as const,
    lineHeight: 26,
    fontFamily: "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
  },
})

export default WelcomeScreen
