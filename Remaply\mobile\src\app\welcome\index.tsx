"use client"

import React from "react"
import { View, StatusBar, TouchableOpacity, Dimensions, Animated } from "react-native"
import { LinearGradient } from "expo-linear-gradient"
import { Text } from "@remaply/frontend-shared"
import NeuralLogo from "./logo" // Import our amazing logo!

const { width: screenWidth, height: screenHeight } = Dimensions.get("window")

interface WelcomeScreenProps {
  navigation: any
}

const ImprovedWelcomeScreen: React.FC<WelcomeScreenProps> = ({ navigation }) => {
  const fadeAnim = React.useRef(new Animated.Value(0)).current
  const slideAnim = React.useRef(new Animated.Value(50)).current

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start()
  }, [])

  const handleGetStarted = () => {
    // Your navigation logic
    console.log("Get Started pressed")
  }

  const handleSignIn = () => {
    // Your navigation logic
    console.log("Sign In pressed")
  }

  return (
    <View style={{ flex: 1, backgroundColor: "#FFFFFF" }}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      {/* Floating background elements */}
      <View style={{ position: "absolute", top: 100, right: -50, opacity: 0.05 }}>
        <View
          style={{
            width: 200,
            height: 200,
            borderRadius: 100,
            backgroundColor: "#14b8a6",
          }}
        />
      </View>
      <View style={{ position: "absolute", bottom: 200, left: -80, opacity: 0.03 }}>
        <View
          style={{
            width: 300,
            height: 300,
            borderRadius: 150,
            backgroundColor: "#0f766e",
          }}
        />
      </View>

      <Animated.ScrollView
        style={{ flex: 1 }}
        contentContainerStyle={{
          flexGrow: 1,
          paddingHorizontal: 24,
          paddingTop: 60,
          paddingBottom: 40,
        }}
        showsVerticalScrollIndicator={false}
        opacity={fadeAnim}
        transform={[{ translateY: slideAnim }]}
      >
        {/* Hero Section */}
        <View style={{ alignItems: "center", marginBottom: 60 }}>
          {/* Neural Logo */}
          <View style={{ marginBottom: 32 }}>
            <NeuralLogo size={140} interactive={true} />
          </View>

          {/* Welcome Text */}
          <View style={{ alignItems: "center", marginBottom: 20 }}>
            <Text
              style={{
                fontSize: 36,
                fontWeight: "800",
                color: "#0f172a",
                textAlign: "center",
                marginBottom: 12,
                letterSpacing: -0.5,
              }}
            >
              Welcome to{"\n"}
              <Text style={{ color: "#14b8a6" }}>Remaply</Text>
            </Text>
            <Text
              style={{
                fontSize: 18,
                color: "#64748b",
                textAlign: "center",
                lineHeight: 28,
                fontWeight: "500",
                maxWidth: 320,
              }}
            >
              AI-powered social media management for Nigerian businesses
            </Text>
          </View>
        </View>

        {/* Features Cards */}
        <View style={{ marginBottom: 50 }}>
          {[
            {
              icon: "⚡",
              title: "Create Once, Share Everywhere",
              description: "Cross-platform content distribution with one click",
              gradient: ["#14b8a6", "#0d9488"],
            },
            {
              icon: "🧠",
              title: "AI-Powered Content Creation",
              description: "Generate engaging content with advanced AI",
              gradient: ["#0f766e", "#134e4a"],
            },
            {
              icon: "📊",
              title: "Smart Analytics & Scheduling",
              description: "Optimize posting times with intelligent insights",
              gradient: ["#06b6d4", "#0891b2"],
            },
          ].map((feature, index) => (
            <Animated.View
              key={index}
              style={{
                marginBottom: 16,
                transform: [
                  {
                    translateX: slideAnim.interpolate({
                      inputRange: [0, 50],
                      outputRange: [0, index % 2 === 0 ? -50 : 50],
                    }),
                  },
                ],
              }}
            >
              <View
                style={{
                  backgroundColor: "#ffffff",
                  borderRadius: 20,
                  padding: 20,
                  flexDirection: "row",
                  alignItems: "center",
                  shadowColor: "#0f172a",
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.08,
                  shadowRadius: 12,
                  elevation: 8,
                  borderWidth: 1,
                  borderColor: "#f1f5f9",
                }}
              >
                <LinearGradient
                  colors={feature.gradient}
                  style={{
                    width: 50,
                    height: 50,
                    borderRadius: 16,
                    justifyContent: "center",
                    alignItems: "center",
                    marginRight: 16,
                  }}
                >
                  <Text style={{ fontSize: 24 }}>{feature.icon}</Text>
                </LinearGradient>
                <View style={{ flex: 1 }}>
                  <Text
                    style={{
                      fontSize: 16,
                      fontWeight: "700",
                      color: "#0f172a",
                      marginBottom: 4,
                    }}
                  >
                    {feature.title}
                  </Text>
                  <Text
                    style={{
                      fontSize: 14,
                      color: "#64748b",
                      lineHeight: 20,
                      fontWeight: "500",
                    }}
                  >
                    {feature.description}
                  </Text>
                </View>
              </View>
            </Animated.View>
          ))}
        </View>

        {/* Action Buttons */}
        <View style={{ gap: 16 }}>
          {/* Primary CTA */}
          <TouchableOpacity
            onPress={handleGetStarted}
            activeOpacity={0.8}
            style={{
              backgroundColor: "#0f172a",
              borderRadius: 16,
              paddingVertical: 18,
              paddingHorizontal: 24,
              shadowColor: "#0f172a",
              shadowOffset: { width: 0, height: 8 },
              shadowOpacity: 0.25,
              shadowRadius: 16,
              elevation: 12,
            }}
          >
            <LinearGradient
              colors={["#0f172a", "#1e293b"]}
              style={{
                position: "absolute",
                left: 0,
                right: 0,
                top: 0,
                bottom: 0,
                borderRadius: 16,
              }}
            />
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Text
                style={{
                  color: "#ffffff",
                  fontSize: 18,
                  fontWeight: "700",
                  marginRight: 8,
                }}
              >
                Get Started
              </Text>
              <Text style={{ color: "#14b8a6", fontSize: 20 }}>→</Text>
            </View>
          </TouchableOpacity>

          {/* Secondary CTA */}
          <TouchableOpacity
            onPress={handleSignIn}
            activeOpacity={0.8}
            style={{
              backgroundColor: "transparent",
              borderRadius: 16,
              paddingVertical: 18,
              paddingHorizontal: 24,
              borderWidth: 2,
              borderColor: "#e2e8f0",
            }}
          >
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Text
                style={{
                  color: "#0f172a",
                  fontSize: 18,
                  fontWeight: "600",
                  marginRight: 8,
                }}
              >
                Already have an account?
              </Text>
              <Text style={{ color: "#14b8a6", fontSize: 18, fontWeight: "700" }}>Sign In</Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Bottom decorative element */}
        <View
          style={{
            alignItems: "center",
            marginTop: 40,
            opacity: 0.6,
          }}
        >
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              gap: 8,
            }}
          >
            <View
              style={{
                width: 8,
                height: 8,
                borderRadius: 4,
                backgroundColor: "#14b8a6",
              }}
            />
            <Text
              style={{
                color: "#64748b",
                fontSize: 14,
                fontWeight: "500",
              }}
            >
              Powered by Neural AI
            </Text>
            <View
              style={{
                width: 8,
                height: 8,
                borderRadius: 4,
                backgroundColor: "#14b8a6",
              }}
            />
          </View>
        </View>
      </Animated.ScrollView>
    </View>
  )
}

export default ImprovedWelcomeScreen
