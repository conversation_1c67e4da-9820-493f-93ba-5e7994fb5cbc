import React, { useEffect, useRef } from "react"
import { View, StatusBar, Dimensions, Animated, StyleSheet } from "react-native"
import { Text } from "@remaply/frontend-shared"
import NeuralLogo from "./logo"

const { width: screenWidth, height: screenHeight } = Dimensions.get("window")

interface WelcomeScreenProps {
  navigation: any
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ navigation }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current
  const slideAnim = useRef(new Animated.Value(30)).current

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start()

    // Navigate to onboarding after 6 seconds
    const timer = setTimeout(() => {
      // navigation.navigate('Onboarding') // Uncomment when onboarding is ready
      console.log("Navigate to onboarding")
    }, 6000)

    return () => clearTimeout(timer)
  }, [navigation])

  return (
    <View style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" translucent={false} />

      {/* Subtle background pattern */}
      <View style={styles.backgroundPattern} />

      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          }
        ]}
      >
        {/* Logo */}
        <View style={styles.logoContainer}>
          <NeuralLogo size={140} interactive={false} />
        </View>

        {/* Brand Name */}
        <Animated.View style={styles.brandContainer}>
          <View style={styles.brandNameWrapper}>
            <Text style={[styles.brandName, styles.brandNamePrimary]}>Re</Text>
            <Text style={[styles.brandName, styles.brandNameSecondary]}>ma</Text>
            <Text style={[styles.brandName, styles.brandNameAccent]}>ply</Text>
          </View>
        </Animated.View>

        {/* Tagline */}
        <Animated.View style={styles.taglineContainer}>
          <View style={styles.taglineWrapper}>
            <Text style={styles.taglinePrefix}>◦ </Text>
            <Text style={styles.tagline}>your ai-powered business management</Text>
            <Text style={styles.taglineSuffix}> ◦</Text>
          </View>
          <View style={styles.taglineUnderline} />
        </Animated.View>
      </Animated.View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF", // Pure white background
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
  },
  backgroundPattern: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.02,
    backgroundColor: "transparent",
    // Create a subtle dot pattern using border
    borderWidth: 1,
    borderColor: "#F0F0F0",
    borderStyle: "dotted",
  },
  content: {
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 40,
  },
  logoContainer: {
    marginBottom: 40,
  },
  brandContainer: {
    marginBottom: 20,
  },
  brandNameWrapper: {
    flexDirection: "row" as const,
    alignItems: "center",
    justifyContent: "center",
  },
  brandName: {
    fontSize: 52,
    fontWeight: "900" as const,
    letterSpacing: -1.5,
    textAlign: "center" as const,
    textShadowColor: "#E0E0E0",
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  brandNamePrimary: {
    color: "#1A1A1A", // Very dark gray
    fontFamily: "'Playfair Display', Georgia, serif", // Elegant serif
    fontStyle: "italic" as const,
  },
  brandNameSecondary: {
    color: "#4A4A4A", // Medium gray
    fontFamily: "'Helvetica Neue', Arial, sans-serif", // Clean sans-serif
    fontWeight: "300" as const,
    transform: [{ scaleY: 1.1 }], // Slightly stretched
  },
  brandNameAccent: {
    color: "#2C2C2C", // Dark gray
    fontFamily: "'Courier New', monospace", // Tech monospace
    fontWeight: "700" as const,
    textDecorationLine: "underline" as const,
    textDecorationColor: "#6E6E6E",
  },
  taglineContainer: {
    paddingHorizontal: 20,
    alignItems: "center",
  },
  taglineWrapper: {
    flexDirection: "row" as const,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  taglinePrefix: {
    fontSize: 14,
    fontWeight: "400" as const,
    color: "#6E6E6E",
    fontFamily: "'Arial', sans-serif",
  },
  tagline: {
    fontSize: 15,
    fontWeight: "400" as const,
    color: "#4A4A4A", // Darker medium gray
    letterSpacing: 0.8,
    textAlign: "center" as const,
    lineHeight: 22,
    fontFamily: "'Avenir Next', 'Helvetica Neue', sans-serif", // Clean, modern font
    fontStyle: "normal" as const,
    textTransform: "lowercase" as const,
  },
  taglineSuffix: {
    fontSize: 14,
    fontWeight: "400" as const,
    color: "#6E6E6E",
    fontFamily: "'Arial', sans-serif",
  },
  taglineUnderline: {
    width: 60,
    height: 1,
    backgroundColor: "#E0E0E0",
    marginTop: 4,
  },
})

export default WelcomeScreen
